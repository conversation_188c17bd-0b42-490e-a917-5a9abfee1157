# 金融监管层次化数据服务

## 1. 项目简介
本项目旨在构建一个面向 **大模型问答 (LLM QA)** 的层次化数据管理与服务平台，
聚焦金融监管场景下的报表及数据字典信息。平台以 **MCP（Model Capability Provider）** 接口
形式向大模型开放查询能力，使大模型能够在问答过程中即时检索并利用权威、结构化的数据。

## 2. 业务背景
金融监管部门要求金融机构按期上报大量 Excel 报表（如*一表通业务*、*监管集市业务*等）。
每个报表文件包含多张 Sheet（如*票据表*、*保函表*等），每张 Sheet 又由一张**数据字典表**
定义字段、口径及口径公式，形成如下层次：

```
报表文件 (Workbook)
└── Sheet
    └── 字段 (ROW)
```

当前痛点：
1. 报表数量多、结构复杂，人工检索字段口径效率低。
2. 无统一接口供 LLM 调用，导致知识孤岛。

## 3. 目标
1. **统一存储**：将报表-Sheet-数据字典-字段的层次结构持久化管理。
2. **高效检索**：支持按关键词、层级、主题等维度查询。
3. **MCP 接口**：提供符合 OpenAPI 规范的接口，供大模型通过插件或 function call 访问。
4. **易扩展**：支持新增报表类型、支持其他行业监管场景。

## 4. 系统架构
```mermaid
graph TB
    LLM["Large Language Model"]
    AdminUI["管理后台 (Admin UI)"]
    API["API 层 (FastAPI)"]
    Service["Service Layer"]
    DB[("数据库 (SQLite)")]
    Vector[("向量库 (Chroma)")]

    LLM -- "REST / MCP OpenAPI" --> API
    AdminUI -- "GraphQL" --> API
    API -- "RPC" --> Service
    Service -- "SQLAlchemy" --> DB
    DB -- "Sync" --> Vector
```

* **API 层**：对外暴露 OpenAPI/MCP 规范接口。
* **Service 层**：核心业务逻辑、查询优化、权限控制。
* **存储层**：SQLite 存储元数据，Chroma 存储向量。
* **管理后台**：导入报表、维护数据字典、权限与审计。

## 5. 技术选型
| 模块 | 技术 | 说明 |
|------|------|------|
| 语言 | Python 3.13 | 快速开发、生态完善 |
| Web 框架 | FastAPI | 高性能、原生 OpenAPI |
| ORM | SQLAlchemy 2.x | 类型安全、支持异步 |
| 数据库 | SQLite 3 | 嵌入式，轻量级，无需独立服务 |
| 向量库 | Chroma | 向量检索，支持 Embedding 相似度 |
| 异步任务 | Celery / RQ | 报表批量导入、指标计算 |

## 6. 数据模型 (ER 简图)
```
file_metadata 1 ── n sheet_metadata 1 ── n row_vectors
```
* `file_metadata(file_id, file_path, status)`
* `file_attributes(file_id, key, value, embedding_text, vector)`
* `sheet_metadata(sheet_id, file_id, sheet_name, table_name, status)`
* `sheet_attributes(sheet_id, key, value, embedding_text, vector)`
* `row_vectors(sheet_id, rowid, file_id, table_name, text, vector)`

索引与全文搜索：使用 SQLite FTS5 支持关键词检索；向量检索由 Chroma 提供。

## 7. MCP / OpenAPI 接口草案
```
GET  /reports            # 报表文件列表
GET  /reports/{id}       # 报表详情 (含 sheets)
GET  /sheets/{id}        # Sheet 详情 (含字段)
GET  /fields/{id}        # 字段详情
GET  /search?keyword=xxx # 关键词跨层级检索
```
接口返回 JSON 并注明 `hierarchy_path` 便于大模型递归追溯。

## 8. 检索与召回策略
| 场景示例 | 召回模式 | 查询链路 |
| -------- | -------- | -------- |
| 票据数据表有哪些字段？ | exact | SQL（SQLite）按 `sheet_name` 精确匹配 `Field` |
| 一共有多少数据表需要上传？ | exact | SQL 聚合统计 `Sheet` 数量 |
| 保函表中按期缴纳费用的统计口径是什么？ | fulltext → exact | SQLite FTS5 匹配口径文本后回溯 `Field` |
| 与利息支出口径相近的字段还有哪些？ | semantic → exact | Chroma 相似度召回，返回 `Field` 详情 |

**说明**：
1. exact：结构化查询，利用索引字段。
2. fulltext：基于 FTS5 的全文检索，对定义/口径文本做分词索引。
3. semantic：将文本转 Embedding 后写入 Chroma，使用余弦相似度召回。

## 9. Excel 导入 + 向量补写 **两阶段流程**

> 新版 Pipeline 推荐 **先入库、后向量** 的两阶段方案，可显著提升首批解析速度，并允许在资源充足时一次性补写向量。

### 可选：清理现有向量

如果需要完全重新生成所有向量（例如，在更换嵌入模型后），可以运行清理脚本。
该脚本会 **删除 `vector_store` 目录** 并 **清空数据库中所有 `vector` 字段**。

```bash
# 清理所有历史向量数据
python scripts/clear_embeddings.py
```
> ⚠️ **警告**: 此操作不可逆，将永久删除所有已生成的向量。请谨慎操作。

### 阶段 ①　结构化入库（无向量）

解析目录下所有 Excel → 识别文件摘要 / 表头 / Sheet 摘要 → 将数据写入 SQLite，**暂不生成任何向量**：

```bash
# 仅写入结构化数据（推荐）
python -m src.ingestion --root data_files --db output/data.db --no-vector

# 如果需要覆盖旧表，可加 --overwrite
python -m src.ingestion --root data_files --db output/data.db --no-vector --overwrite
```

CLI 会显示 File / Sheet 两级进度条；执行完成后可在 `output/data.db` 中看到已解析的表结构与文字信息，`vector` 字段全部为 `NULL`。

### 阶段 ②　批量补写向量并同步 Chroma

当本地 GPU/Embedding 服务就绪后，执行补写脚本一次性为 **缺失向量** 的记录生成 Embedding，并（可选）立即写入 Chroma：

```bash
# 只补写 SQLite 中缺失的向量
python -m src.vectorization.backfill_vectors --db output/data.db

# 补写后立即同步到 Chroma（持久化目录 vector_store）
python -m src.vectorization.backfill_vectors --db output/data.db --sync --persist vector_store
```
### 其他方法

重新将db中的向量，同步到向量库以确保所有向量都被正确索引：

```bash
python -m src.vectorization.sync_chroma
```

清除db中所有向量字段，以重新生成 embedding

```bash
python scripts/clear_embeddings.py
```

脚本会依次处理：
1. `file_attributes`　（文件级描述等）
2. `sheet_attributes`（Sheet 描述、列口径常量等）
3. `row_vectors`　 （行级文本）

每批次默认 256 条，可用 `--batch` 调整。完成后若启用 `--sync`，内部会调用 `src.vectorization.sync_chroma` 将新向量写入 Chroma 向量库。

> 若仍希望"一步到位"同时生成向量，可继续使用 `python -m src.ingestion --vector`，但如果耗时过长，中途中断，可能会造成问题。

## 10. 大模型服务可用性测试

在启动服务前，可先运行内置脚本对 **Embedding 服务** 与 **对话大模型** 的连通性进行快速健康检查：

```bash
python scripts/test_model_services.py
```

脚本会读取 `config/app_settings.yaml` 中的 `tasks` 配置项，分别对：
1. `embedding` 任务 —— 调用 `Vectorizer.embed_texts` 生成一次向量；
2. 其余 LLM 任务 —— 调用 `src.parser.llm.client.ask` 发送简短问候。

脚本最终会统计每个调用的耗时并以表格方式输出结果，例如：

```
┏━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━┳━━━━━━━━━━━━┓
┃ Task                  ┃ Provider ┃ Model    ┃ Status ┃ Latency (s) ┃
┡━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━╇━━━━━━━━━━━━┩
│ embedding             │ ollama   │ nomic-embed │ ✅ OK │ 0.12 │
│ qa_answer             │ openai   │ gpt-4o      │ ✅ OK │ 0.65 │
└───────────────────────┴──────────┴──────────┴────────┴────────────┘
```

当所有行均显示 `✅ OK` 时，即可放心执行后续向量化、QA Pipeline 或 API 服务。
若连接失败，请检查网络、API Key 或本地模型服务状态后重试。

## 智能问答 API

```bash
# 启动服务（根目录）
uvicorn src.server:app --reload

# 调用示例
curl -X POST http://localhost:8000/ask -H 'Content-Type: application/json' -d '{"question": "库存为 0 的 SKU 有哪些？"}'
```

## 新版 QA Pipeline (v0.3+)

| 层级 | 组件 | 说明 |
| ---- | ---- | ---- |
| Fixed | `intent_classifier` | 基于 YAML 规则做意图识别 |
| Dynamic | `vector_recall` | 向量召回 Sheet IDs |
| Dynamic | `mcp_query` | 根据 Sheet 预览表数据 |
| Dynamic | `llm_answer` | 生成最终回答 |

Pipeline 由 `config/pipeline.yaml` 描述；可通过修改该文件增删组件。

运行示例：
```bash
python -m src.qa.pipeline
```

## 层次化数据存储结构

本项目实现了一个层次化的数据存储结构，用于高效地组织和检索层次化的表格数据。

### 存储层次

数据按照以下层次进行组织：

1. **文件层（File Level）**
   - 每个文件拥有一个唯一的 `file_id`
   - 文件属性以 key-value 形式存储在 `file_attributes` 表中
   - 每个属性都可以进行向量化，用于语义检索

2. **表格层（Sheet Level）**
   - 每个 Excel 表格拥有一个唯一的 `sheet_id`
   - 表格属性以 key-value 形式存储在 `sheet_attributes` 表中
   - 表格的列信息被存储为特殊属性（`column:{name}`）
   - 每个属性都可以进行向量化，用于语义检索

3. **行数据层（Row Level）**
   - 行数据存储在各自的数据表中
   - 行的向量化表示存储在 `row_vectors` 表中
   - 每行数据关联到特定的 `sheet_id` 和 `file_id`

### 数据库结构

```
file_metadata
  - file_id (PK)
  - file_path

file_attributes
  - file_id (FK)
  - key
  - value
  - vector

sheet_metadata
  - sheet_id (PK)
  - file_id (FK)
  - sheet_name
  - table_name

sheet_attributes
  - sheet_id (FK)
  - key
  - value
  - vector

row_vectors
  - sheet_id (FK)
  - rowid
  - file_id (FK)
  - table_name
  - vector
  - text
```

---
> 本文档为初步规划，后续根据业务演进持续迭代。 