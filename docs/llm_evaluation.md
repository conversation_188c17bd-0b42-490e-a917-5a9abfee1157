# LLM 评估报告（阶段性）

> 版本：v0.1  更新日期：{{DATE}}

本报告汇总 T3–T6 实验结果及性能基准，后续将补充 T1–T2 精度与更多案例。

---

## 1. 任务矩阵

| 任务 | 描述 | 输入 | 期望输出 |
| ---- | ---- | ---- | -------- |
| T1 | 表类型判定 | 表格元数据 + 样例 | `{"type": "余额表"}` |
| T2 | Header 行/列识别 | 表格片段 | `{"header_rows": [1,2]}` |
| T3 | 字段语义映射 | 字段中文+描述 | `["EAST:AcctBal"]` |
| T4 | 说明/校验段提取 | 备注单元格文本 | `[{"section":"校验规则","content":"..."}]` |
| T5 | 异常值检测 | 样例 JSON | `[{"row":3,"field":"余额","reason":"负数"}]` |
| T6 | 多行标题合并 | 多行标题片段 | `["序号","版本号",...]` |

详细协议与 JSON Schema 见《智能解析器设计方案》附录。

---

## 2. 精度结果

| 任务 | 测试集大小 | 准确率 / F1 | 备注 |
| ---- | ---------- | ----------- | ---- |
| T3 | 78 字段 | **92.3%** | 字典映射（人工校对） |
| T4 | 45 段落 | **88.9%** | 多标签分类 |
| T5 | 30 行 × 4 表 | **0.92** F1 | 以异常行级别计算 |
| T6 | 12 表 | **100%** | 完全匹配字段序 |

> 注：T1/T2 尚在调优中，结果稍后补充。

---

## 3. 性能/成本基准

以本地 Ollama `qwen3:latest`（8B）为例，测量不同 Prompt 截断长度与批量大小组合的总耗时与费用估算（USD）。成本估算公式：

```
Cost = (input_tokens / 1K) * 0.0001 + (output_tokens / 1K) * 0.0003
```

| Token 长度 | Batch | 平均耗时 (s) | Input tokens | Output tokens | 估算成本 |
| ---------- | ----- | ------------ | ------------ | ------------- | -------- |
| 256 | 1 | 2.90 | 256 | 155 | $0.0001 |
| 256 | 4 | 8.97 | 1 024 | 467 | $0.0002 |
| 256 | 8 | 21.67 | 2 048 | 1 143 | $0.0006 |
| 512 | 1 | 2.57 | 512 | 128 | $0.0001 |
| 512 | 4 | 10.87 | 2 048 | 551 | $0.0004 |
| 512 | 8 | 20.08 | 4 096 | 1 023 | $0.0007 |
| 1 024 | 1 | 2.39 | 1 024 | 118 | $0.0001 |
| 1 024 | 4 | 10.03 | 4 096 | 497 | $0.0006 |
| 1 024 | 8 | 19.99 | 8 192 | 995 | $0.0011 |

> 测试脚本：`python -m src.experiments.llm_perf`

---

## 4. 主要观察

1. **批量化收益**：Batch=4 时吞吐/时延折中最佳；Batch=8 进一步提升吞吐但单次时延翻倍。  
2. **Prompt 长度影响有限**：在本地 Qwen3 8B 上，256〜1024 token 对推理时延差异不大。  
3. **成本极低**：单批迭代成本低于 $0.002，可放心大规模实验。

---

## 5. 下一步计划

1. 补充 T1/T2 精度评估；  
2. 迁移至 SiliconFlow 云端 70B 模型比对；  
3. 探索 `instructor` 库自动解析 JSON，减少正则后处理。 