# 数据存储与向量化架构

> 适用项目：`a_hierarchical_data_server`  
> 文档版本：v1.0（2025-09-20）  
> 本文件整合原《数据存储整体架构》与《Excel 自动解析与向量化存储方案设计》中有关“存储、向量化、向量数据库同步”的全部内容，作为解析结果落地的唯一权威说明。

---

## 1 总览
解析得到的 `ParsedTable` 将依次写入本地 **SQLite**（结构化 & 向量化数据）并同步到 **Chroma** 向量数据库，形成如下三层架构：

```mermaid
graph LR
  A[Excel 文件] -->|解析| B(SQLite 动态表)
  B --> C[SQLite 元数据表]
  C -->|Vectorizer| D[行/层级向量列]
  D -->|sync_chroma.py| E[Chroma sheet_vectors Collection]
```

* **结构化层**：每个有效 Sheet → 1 张动态业务表，字段与行数据完整落地。  
* **元数据层**：固定 5 张表记录文件/Sheet/行元数据与向量。  
* **向量检索层**：Chroma 基于 DuckDB+Parquet 持久化，负责 ANN 查询。


## 2 关系型存储（SQLite: `output/data.db`）

### 2.1 元数据表
| 表 | 关键字段 | 说明 |
| --- | --- | --- |
| `file_metadata` | `file_id` PK, `file_path` | Excel 文件信息 |
| `file_attributes` | `(file_id, key)` PK, `value`, `embedding_text`, `vector` | 文件属性及向量 |
| `sheet_metadata` | `sheet_id` PK, `file_id`, `sheet_name`, `table_name` | Sheet 基础信息 |
| `sheet_attributes` | `(sheet_id, key)` PK, `value`, `embedding_text`, `vector` | Sheet 属性及向量 |
| `row_vectors` | `(sheet_id, rowid)` PK, `table_name`, `vector`, `text` | 行级展平文本向量 |

**说明**  
1. 向量列统一存 `float32[] -> BLOB`。  
2. `key` 采用前缀命名：`description`、`column:{name}`、`original_description` 等。  
3. `table_name` 与动态表同名，由解析器 `ExcelParser._generate_table_name()` 生成。

### 2.2 动态业务表
- 每个业务 Sheet 建一张表，列直接映射 DataFrame.columns。  
- 主键使用 SQLite 隐式 `rowid`（方便行向量关联）。

### 2.3 关键索引与约束
* `file_metadata.file_path` UNIQUE 保障文件去重。  
* `(file_id, sheet_name)` UNIQUE 避免同文件重名 Sheet。  
* 行向量表 `(sheet_id, rowid)` 与业务表共享 `rowid`，保证一一对应。  
* 可在 `file_attributes.vector`、`sheet_attributes.vector` 上建立 ANN 索引（如 `sqlite_ann`）。


## 3 向量存储（Chroma: `vector_store/`）

### 3.1 ID 命名规则
| 级别 | Chroma `id` | 对应记录 | `metadata.level` | 备注 |
| ---- | ----------- | -------- | ---------------- | ---- |
| 文件属性 | `file:{file_id}:{key}` | `file_attributes` | `file` | 文件描述、主题等 |
| Sheet 摘要 | `sheet:{sheet_id}` | `sheet_attributes` (key=`description`) | `sheet` | Sheet 级摘要 |
| Sheet 属性 | `sheet:{sheet_id}:{key}` | `sheet_attributes` | `sheet` | 任意属性 |
| 列常量 | `const:{table_name}:{column}` | `sheet_attributes` (key=`column:{name}`) | `column_constant` | 枚举/常量列 |
| 行数据 | `{sheet_id}:{rowid}` | `row_vectors` | `row` | 行级语义向量 |

### 3.2 Metadata 字段
所有向量写入同一 collection `sheet_vectors`，统一附带：
```jsonc
{
  "file_id": 1,
  "sheet_id": 3,        // 文件级向量可为空
  "table_name": "tb1", // 行/列向量
  "column_name": "remark", // 仅列常量
  "rowid": 25,          // 仅行向量
  "level": "row",      // file / sheet / sheet_desc / table / column_constant / row
  "key": "description" // 属性名
}
```

### 3.3 向量同步流程
1. **解析阶段即时写库**：行向量与元数据向量产生后先存入 SQLite 对应表。  
2. **离线同步**：执行
   ```bash
   python -m src.vectorization.sync_chroma \
       --db output/data.db --persist vector_store --batch 2048
   ```
   该脚本按批读取向量并调用 Chroma `add()` 写入，如向量已存在则跳过。  
3. **增量更新**：文件变更检测 → 仅同步受影响 Sheet/行所属的向量。


## 4 向量化策略
### 4.1 行级向量
* **序列化**：`col1: v1; col2: v2; ...`（仅保留需要语义检索的列）。  
* **模型**：`qwen3-embedding 4B` via Ollama `/api/embeddings`。  
* **批大小**：64~128 行/批，单事务写库。

### 4.2 文件 & Sheet 摘要向量
* 解析完成后生成 100~150 字摘要，随后嵌入。  
* 摘要文本存 `embedding_text`，原始值存 `value`，二者可能一致。


## 5 检索 & 查询指南
| 需求 | 推荐层级 | 说明 |
| --- | --- | --- |
| 表主要内容？ | `sheet_desc` → `sheet` | 描述行最贴近“记录什么” |
| 文件主题？ | `file` (`description`) | 文件级摘要 |
| 列含义？ | `column_constant` / `sheet_desc` | 先查列属性，无则降级 |
| 条件过滤 + 相似度检索 | 结构化 SQL + `row` | 混合检索 |

**通用流程**：
1. 查询向量 → 文件 / Sheet 近邻召回。  
2. 在候选 Sheet 的行向量内再做 ANN，返回精确行。  
3. 如需跨文件，可先在文件级检索后下钻 Sheet。  


## 6 维护策略
1. **级联删除**：删除文件时级联删除其 Sheets、行数据及所有相关向量。  
2. **备份**：定期复制 `output/data.db` 与 `vector_store/` 目录。  
3. **性能优化**：
   * Chroma 可切换 `Settings(chroma_db_impl="duckdb+parquet", persist_directory="vector_store")`。  
   * 大规模部署可替换为 Milvus / Qdrant，实现方式参见 `vectorization.vector_store` 抽象。

---

> 解析流程详见《Excel 解析流水线设计》。两份文档共同构成从“Excel → 数据库 → 向量数据库”全链路规范。 