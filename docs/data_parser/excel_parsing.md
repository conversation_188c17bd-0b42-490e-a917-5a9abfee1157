# Excel 解析流水线设计

> 适用项目：`a_hierarchical_data_server`  
> 文档版本：v1.1（2025-09-21）  

---

## 1 目标
1. **自动发现** `data_files/` 目录中的 `.xlsx` 文件。  
2. **智能识别** 工作表类型、表头行、字段含义及数据区范围。  
3. **结构化输出** `pandas.DataFrame` 及元数据（字段类型、行号映射）。  
4. **高可插拔** 规则 + 8B 中文 LLM 协同解析（回退启发式）。  
5. **可追溯** 全过程日志、异常行定位，便于人工校正。


## 2 输入 / 输出
| 阶段 | 输入 | 输出 |
| ---- | ---- | ---- |
| 文件发现 | `data_files/*.xlsx` | 文件路径列表 |
| 解析 | 单个 Excel 文件 | `list[ParsedTable]` *(含 DataFrame + meta)* |
| 存储 | *见《数据存储与向量化架构》* | SQLite 动态表 + 元数据表 |

`ParsedTable` 数据类草案：
```python
@dataclass
class ParsedTable:
    name: str                 # 建议：{文件名}_{sheet名}
    dataframe: pd.DataFrame   # 结构化数据
    meta: dict                # description_rows, title_rows, data_start_row 等
```


## 3 典型 Sheet 结构分析
| Sheet 类型 | 代表工作表 | 结构特征 | 解析关注点 |
|------------|------------|----------|------------|
| 业务数据表 | `机构信息表`、`个人存款分户账` | • 前若干行说明文字<br/>• 合并单元格标题<br/>• 数据区行数多 | 锁定 header 行、展开合并单元格、忽略说明行 |
| 码表/枚举表 | `码值映射`、`行业码值映射` | 首行即字段标题，列数少 | 确保存在 “代码 / 名称” 列 |
| 文本说明表 | `数据元说明` | 多行长文本，无固定列 | 作为纯文本存储 |
| 目录/变更表 | `版本变更记录` | 与业务表类似 | 普通数据表处理 |


## 4 解析流程总览
```mermaid
flowchart TD
    A[发现 Excel 文件] --> B[LLM + 规则判断表头]
    B --> C[拆并 & 列名规范化]
    C --> D[数据区裁剪]
    D --> E[DataFrame 生成]
    E --> F[ParsedTable 列表]
```

* **LLM + 规则判断表头**：先启发式估计，再调用本地 LLM 检测 `title_rows` / `data_start_row`；若失败回退规则。  
* **拆并 & 列名规范化**：取消所有合并单元格并复制值，构建多级列名后规范化去重。  
* **数据区裁剪**：根据 `data_start_row` 截取有效数据区。  
* **DataFrame 生成**：空列剔除、类型推断、异常行日志。


## 5 详细处理流程
0. **取消合并单元格**  
   * 使用 OpenPyXL 展开每个合并区域；  
   * 将左上角单元格的值复制到该区域其它单元格；  
   * 这样行/列统计与 LLM 输入不受合并影响。

1. **定位表头**  
   * 找到首行非空比例 >60% 的行视为候选；  
   * 与 LLM 返回的 `title_rows` 取交集；若冲突按规则优先。

2. **列名规范化**  
   * 空列名 → `col_{idx}`；重复列名追加后缀；全部小写、下划线分隔。

3. **数据区截取**  
   * 从 `data_start_row` 开始，至连续空行结束。

4. **字段描述行合并**  
   * 若字段名称分布在两行（名称 + 说明），拼接为单行列名，如 `名称 / 说明`。

5. **整列常量检测**  
   * 若某列所有值均相同且文本长度 ≥30 字符（常见于整列合并单元格），视为 **列常量**；  
   * 将文本作为 *Sheet 属性* 写入 `sheet_attributes (key="column:{name}")`；  
   * 同时向量化并写入 Chroma，`id="const:{table_name}:{column}"`，`metadata.level="column_constant"`。

6. **类型推断**  
   * 使用 `pandas` 自动推断，补充日期、金额、百分比等自定义规则。

7. **异常记录**  
   * 无法解析的行写入 `row_errors`，但整体流程不中断。


## 6 核心代码文件
| 文件 | 关键职责 | 主要接口 |
|------|----------|----------|
| `src/ingestion/excel_parser.py` | 解析器；合并拆分、LLM 表头识别、常量列提取 | `ExcelParser.parse_excel()` |
| `src/ingestion/pipeline.py` | 串联解析 ➔ 存储 ➔ 向量化 | `Pipeline.run()` |
| `src/ingestion/summarizer.py` | 文件 / Sheet / 表块摘要生成 | `summarize_*()` |
| `src/storage/db.py` | SQLite 写入 + 向量列封装 | `DBWriter.*` |
| `src/vectorization/` | 向量化与向量库适配 | `Vectorizer.embed_texts()` 等 |


## 7 CLI 与目录结构
解析 CLI：
```bash
python -m src.ingestion --root data_files --db output/data.db --overwrite
```

项目内主要代码分布：
```
src/
  ingestion/
    excel_parser.py        # 解析核心
    pipeline.py            # 调用 ExcelParser & DBWriter
    summarizer.py
    runner.py              # Rich CLI 入口
  storage/
    db.py                  # SQLite 封装
  vectorization/
    vectorizer.py          # 调用 Ollama
    vector_store.py        # Chroma 适配
  utils/
    config.py
    logger.py
```

---

> 下一步：请参阅《数据存储与向量化架构》了解结构化结果的持久化、向量化及向量数据库同步流程。 