# QA Pipeline 设计说明

> 适用版本：v1.0+

最新版 QA Pipeline 采用 **全固定节点** 策略，所有组件顺序完全由配置文件 `config/qa_pipeline.yaml` 决定，简化了早期“固定 + 动态”混合式架构带来的复杂性，降低了推理时延并提升可控性。

---

## 1. 关键概念

| 名称 | 说明 |
| --- | --- |
| Step | Pipeline 中的一个处理节点，包含 `name` 与 `config`。|
| ComponentRegistry | 组件注册表，负责 `name → class` 的映射。 |
| MixedPipeline | 基础 Pipeline 构造器，读取 YAML 后依次实例化组件并执行。 |
| DynamicOrchestrator | QA 专用 Orchestrator，内部持有 `MixedPipeline`，对外暴露 `answer()` 接口。 |
| ShortPass | 当 Pipeline 早期阶段已获得 `row_texts`（例如 `data_item_identifier` 命中目标行）时立即触发，跳过后续召回，直接调用 `llm_answer`。 |
| QAPipeline | 交互式脚本，利用 `DynamicOrchestrator` 并以 Live UI 展示执行进度。 |

> 设计原则：**最小可信闭环** & **简化依赖链**。

---

## 2. 配置文件示例 `config/qa_pipeline.yaml`

```yaml
fixed_steps:
  - name: intent_classifier       # 意图分类
  - name: sheet_name_classifier   # 精确匹配表名（提高召回精度）
  - name: data_item_identifier    # 字段识别 + 行级直查（可触发 Short Pass）
  - name: row_vector_recall       # 多层级向量检索
  - name: rerank                  # 对行文本按 BM25/关键词重排
  - name: llm_answer              # 合成最终答案
```

**要点说明**

1. 新增 `sheet_name_classifier` 与 `data_item_identifier` 组件，用于在 **检索前** 精确锁定表/字段。
2. 若 `data_item_identifier` 已写入 `row_texts`，将触发 **Short Pass**，直接跳转至 `llm_answer`，显著降低时延。
3. 保持 `fixed_steps` 单一入口；如需动态规划仍可通过 `dynamic_candidates` 扩展，但默认无需关心。

---

## 3. Pipeline 构建流程

```mermaid
graph TD
    subgraph Config
        A[加载 qa_pipeline.yaml]
    end
    A --> B[解析 fixed_steps]
    B --> C[ComponentRegistry 实例化]
    C --> D[依序执行组件]
```

1. **加载配置**：应用启动时读取 `qa_pipeline.yaml`。
2. **实例化组件**：遍历 `fixed_steps`，通过 `ComponentRegistry` 创建对象。
3. **Pipeline 执行**：按 YAML 顺序依次调用每个组件的 `run()` 方法，并累积到共享 `payload`。

---

## 4. 参考代码片段

### 4.1 MixedPipeline 构造（简化版）

```python
from pathlib import Path
from typing import Dict, Any, List
import yaml

from orchestration.registry import ComponentRegistry

class MixedPipeline:
    def __init__(self, cfg_path: str = "config/qa_pipeline.yaml"):
        self.cfg_path = Path(cfg_path)
        self.cfg: Dict[str, Any] = self._load_yaml()
        self.steps: List[Dict[str, Any]] = self.cfg.get("fixed_steps", [])
        self.components = []

    def _load_yaml(self) -> Dict[str, Any]:
        if not self.cfg_path.exists():
            raise FileNotFoundError(self.cfg_path)
        with self.cfg_path.open("r", encoding="utf-8") as f:
            return yaml.safe_load(f) or {}

    def build(self) -> None:
        self.components = [
            ComponentRegistry.get(step["name"])(step.get("config", {}))
            for step in self.steps
        ]

    def execute(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        data = dict(payload)
        for comp in self.components:
            data.update(comp.run(data))
        return data
```

该实现与源码 `src/orchestration/pipeline.py` 保持一致，仅省略了与动态节点相关的逻辑。

### 4.2 DynamicOrchestrator 调用示例

```python
from orchestration.pipeline import MixedPipeline

pipeline = MixedPipeline("config/qa_pipeline.yaml")
pipeline.build("请帮我总结 G01 资产负债项目表的主要异常")
result = pipeline.execute({"query": "请帮我总结 G01 资产负债项目表的主要异常"})
print(result["answer"])
```

---

## 5. 典型场景示例

| 用户需求 | 执行 Pipeline | 说明 |
| --- | --- | --- |
| “这张表的数据有哪些异常？” | intent_classifier → vector_recall → rerank → llm_answer | 向量检索 + 关键词重排，再交给 LLM 汇总 |
| “帮我总结这份 pdf” | intent_classifier → llm_answer | 若检索返回空结果，LLM 会直接说明无法回答 |

---

## 6. 扩展与测试

1. **新增组件**：
   - 编写继承 `BaseComponent` 的类，并在模块加载时通过 `ComponentRegistry.register()` 注册。
   - 在 `qa_pipeline.yaml` 中新增条目并配置参数即可。
2. **单元测试**：
   - Mock 组件验证执行顺序与数据流。
   - 使用示例问题跑回归测试，确保输出稳定。
3. **灰度开关**：
   - 通过环境变量 `QA_PIPELINE_CFG` 指定不同 YAML，实现 A/B Test。

---

> 如有任何疑问或改进建议，请在 `docs/` 提交 PR 或在 issue 讨论。 