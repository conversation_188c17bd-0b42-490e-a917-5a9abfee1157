# 系统分层架构设计

> 版本：0.1   更新时间：2025-07-21

本文档总结了当前项目（尤其是 **QA 系统**）的整体架构分层、各层职责、动态编排边界以及与典型 LLM-Agent 的映射关系，供后续设计与实现参考。

---

## 1. 四层分层模型

| 层级 | 主要关注点 | 典型示例 |
|------|------------|----------|
| **System** | 业务域/产品形态，稳定的外部契约 | `qa`、`ingestion`、`evaluation` |
| **Pipeline** | 端到端业务流程，由多个 Component 串联，可嵌套子 Pipeline | `qa_pipeline`、`ingestion_excel_pipeline` |
| **Component** | 业务语义明确的功能模块，封装若干 Capability，对外暴露稳定接口 | `McpQueryComponent`、`VectorRecallComponent` |
| **Capability** | 最底层原子能力，一次 LLM 调用 / DB 查询 / 向量检索等 | `McpClient.run_sql()`、`vectordb.query()` |

### 1.1 设计动机

1. 将“大而杂”的需求拆分为可独立演进的小单元，降低耦合度。
2. 不同层级关注点/生命周期不同，便于测试、监控与灰度发布。
3. 通过 **子 Pipeline 作为“大粒度 Component”**，应对复杂分支而不破坏整体清晰度。

---

## 2. 动态性与静态性边界

| 层级 | 是否允许 LLM 动态决策 | 建议 | 原因 |
|------|----------------------|------|------|
| Capability | 否 | 固定 | 原子操作应稳定、可缓存；避免接口频繁变动导致爆炸式 Prompt 更新 |
| Component | *部分动态*（仅参数可变） | 组件职责与边界固定，参数可 runtime override | 统一输入输出 Schema；重试、熔断、指标采集等横切逻辑在此层实现 |
| **Pipeline** | **是** | 允许由 Planner/Agent 动态编排 | 业务语义集中，变动集中于流程编排，对底层稳定性影响最小 |
| System | 否 | 固定 | 属于产品形态承诺，不宜频繁变更 |

> **核心原则：** *“仅 Pipeline 层具有动态性，其余层保持静态或配置化”*，从而在“灵活”与“可维护”之间取得平衡。

---

## 3. 与典型 LLM-Agent 的映射

| Agent 概念 | 本架构映射 | 说明 |
|------------|-----------|------|
| Tool / Action | **Component** | LLM Planner 可直接选择并填充参数 |
| Observation | Component 输出 | 已封装为结构化数据，供后续推理 |
| Tool 内部逻辑 | Capability | 对 LLM 隐藏，实现细节可自由演进 |

**调用链示例：**

```
LLM Planner → 选择 McpQueryComponent
          → 组件内部调用 McpClient.run_sql()（Capability）
          → 返回查询结果给 Planner
          → Planner 决定下一步（如向量召回 / 直接回答）
```

这样既兼容主流 Agent “工具调用”范式，又确保底层实现可控、易监控。

---

## 4. 实践落地建议

1. **Registry + Factory**：在 *system / pipeline / component* 三级建立注册表；新增能力只影响本级目录。
2. **Pipeline DSL/YAML**：使用声明式配置描述步骤；`DynamicOrchestrator` 解析后按序执行。分支/循环由 `LLM Planner` 决策。
3. **组件参数化**：组件接口稳定，仅暴露必要参数（如 `top_k`、`temperature`）；重试/缓存/指标在内部完成。
4. **日志与指标**：
   - `pipeline_id` & `run_id` 贯穿全链路。
   - Component 级别采集 cost、latency、调用次数，为 Planner 提供回溯数据。
5. **灰度与回滚**：
   - Pipeline 层支持蓝绿发布或灰度流量。
   - Component/Capability 变更先在影子 Pipeline 中验证。

---

## 5. 未来演进

- 当需要“多轮 Agent Loop”时，可在 Pipeline 内引入 **子 Pipeline == Agent Loop**；对上层仍表现为单一 Component。
- 对极端细粒度控制需求，可封装成新的 Component，而非直接暴露 Capability。
- 随着组件增多，考虑自动生成 **Tool 文档** 与 **OpenAPI Schema**，减轻 Prompt 维护成本。

---

> 结论：**“把工具箱给大模型，而不是把所有螺丝刀零件都倒在它面前。”**

通过仅在 Pipeline 层开放动态编排，我们能够在提升智能性的同时，保持系统稳定、可观测、易维护。
