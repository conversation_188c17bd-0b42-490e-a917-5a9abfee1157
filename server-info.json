{"name": "LineageMCP", "instructions": null, "fastmcp_version": "2.10.6", "mcp_version": "1.12.2", "server_version": "2.10.6", "tools": [{"key": "query_lineage", "name": "query_lineage", "description": "查询指定表字段的数据血缘信息。\n\nParameters\n----------\ntable : str\n    数据表名称。\nfield : str\n    字段名称。\n\nReturns\n-------\nstr\n    该字段的数据血缘说明，若未找到则返回默认提示。", "input_schema": {"properties": {"table": {"title": "Table", "type": "string"}, "field": {"title": "Field", "type": "string"}}, "required": ["table", "field"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}], "prompts": [], "resources": [], "templates": [], "capabilities": {"tools": {"listChanged": true}, "resources": {"subscribe": false, "listChanged": false}, "prompts": {"listChanged": false}, "logging": {}}}