#!/usr/bin/env python3
"""rebuild_fts.py

重新生成 row_vectors_fts 索引，解决中文无法匹配的问题。
做法：
1. 读取 row_vectors(rowid,text)
2. 对 text 做简易分词：对每段连续中文生成长度2-4的子串 token，
   非中文保持原单词；把所有 token 以空格连接。
3. 清空 row_vectors_fts 表，批量插入 (rowid, seg_text)。

运行：
    python scripts/rebuild_fts.py --db output/data.db
"""
from __future__ import annotations

import argparse
import re
import sqlite3
from typing import Iterable
from tqdm import tqdm  # type: ignore
import jieba


def segment_cn(text: str) -> str:  # noqa: D401
    """使用 jieba 对中文进行精准分词。"""
    tokens: list[str] = []
    # 保留原英文/数字 token
    tokens.extend(re.findall(r"[A-Za-z0-9]+", text))
    # jieba 分词
    tokens.extend([w for w in jieba.cut(text, cut_all=False) if w.strip()])
    # 去重保持顺序
    seen: set[str] = set()
    uniq = [t for t in tokens if not (t in seen or seen.add(t))]
    return " ".join(uniq)


def rebuild(db_path: str, chunk: int = 1000) -> None:  # noqa: D401
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()

    total = cur.execute("SELECT COUNT(*) FROM row_vectors").fetchone()[0]
    print(f"总行数: {total}")

    # 若 FTS 表已存在先删除，防止损坏
    cur.execute("DROP TABLE IF EXISTS row_vectors_fts;")
    print("创建新的 row_vectors_fts 索引表 ...")
    cur.execute("CREATE VIRTUAL TABLE row_vectors_fts USING fts5(src_id UNINDEXED, text);")
    conn.commit()

    # 分批插入
    for offset in tqdm(range(0, total, chunk)):
        rows = cur.execute(
            "SELECT rowid, text FROM row_vectors LIMIT ? OFFSET ?", (chunk, offset)
        ).fetchall()
        seg_rows: list[tuple[int, str]] = [
            (rid, segment_cn(txt)) for rid, txt in rows  # type: ignore[misc]
        ]
        cur.executemany(
            "INSERT OR REPLACE INTO row_vectors_fts(src_id, text) VALUES (?, ?)", seg_rows
        )
        conn.commit()

    print("FTS 重建完成！")
    conn.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--db", default="output/data.db", help="SQLite DB 路径")
    args = parser.parse_args()
    rebuild(args.db) 