#!/usr/bin/env python3
"""Quick connectivity test for all model services declared in config.

Run: ``python scripts/test_model_services.py``
"""

import sys
import time
from pathlib import Path
from typing import Dict, Any

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import box
import yaml  # Added for loading additional YAML config
import concurrent.futures

# ------------------------------------------------------------------
# Ensure project root is importable when script is executed directly
# ------------------------------------------------------------------
PROJECT_ROOT = Path(__file__).resolve().parent.parent
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

from src.utils.config import load_config  # noqa: E402

console = Console()


def _test_embedding(task_cfg: Dict[str, Any]) -> tuple[str, float]:  # noqa: D401
    """Return (status, latency_s)."""
    start = time.time()
    try:
        from src.vectorization.vectorizer import Vectorizer

        vec = Vectorizer().embed_texts(["测试向量"])[0]
        if not vec:
            raise RuntimeError("empty vector returned")
        status = "✅ OK"
    except Exception as exc:  # noqa: BLE001
        status = f"❌ {exc}"
    latency = time.time() - start
    return status, latency


def _test_llm(task_name: str, task_cfg: Dict[str, Any]) -> tuple[str, float]:  # noqa: D401
    start = time.time()
    try:
        from src.parser.llm.client import ask, LLMClientError

        system_msg = (
            task_cfg.get("system_prompt")
            or task_cfg.get("prompt")
            or "You are a helpful assistant."
        )
        # 简短 user prompt，避免不必要 token
        rsp = ask(
            "你好，请回复 OK。",
            system=system_msg,
            provider=task_cfg.get("provider"),
            model=task_cfg.get("model"),
            temperature=0,
            max_tokens=16,
        )
        if not rsp.strip():
            raise RuntimeError("empty response")
        status = "✅ OK"
    except LLMClientError as ex:
        status = f"❌ LLM error: {ex}"
    except Exception as exc:  # noqa: BLE001
        status = f"❌ {exc}"
    latency = time.time() - start
    return status, latency


def _test_rerank(task_cfg: Dict[str, Any]) -> tuple[str, float]:  # noqa: D401
    """Test SiliconFlow rerank endpoint connectivity."""
    import json
    import os
    import requests  # noqa: WPS433

    start = time.time()
    try:
        # Resolve endpoint: prefer explicit field, else from llm_services
        endpoint = task_cfg.get("endpoint")
        if not endpoint:
            cfg = load_config()
            endpoint = (
                cfg.get("llm_services", {})  # type: ignore[arg-type]
                .get("siliconflow", {})
                .get("rerank_endpoint")
            )

        if not endpoint:
            raise RuntimeError("rerank endpoint not configured")

        payload = {
            "model": task_cfg.get("model"),
            "query": "测试",
            "documents": ["示例文档A", "示例文档B"],
        }

        # Authorization header if API key available
        cfg = load_config()
        key_env = (
            cfg.get("llm_services", {})  # type: ignore[arg-type]
            .get("siliconflow", {})
            .get("api_key_env", "SILICONFLOW_API_KEY")
        )
        headers = {"Content-Type": "application/json"}
        api_key = os.getenv(key_env)
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        resp = requests.post(endpoint, headers=headers, data=json.dumps(payload), timeout=15)
        resp.raise_for_status()
        data = resp.json()
        # Expect 'results' key in successful response
        if "results" not in data:
            raise RuntimeError("unexpected response structure")
        status = "✅ OK"
    except Exception as exc:  # noqa: BLE001
        status = f"❌ {exc}"
    latency = time.time() - start
    return status, latency


# ------------------------------------------------------------
# Unified runner for threading convenience
# ------------------------------------------------------------


def _run_task(task_name: str, task_cfg: Dict[str, Any]) -> tuple[str, float]:  # noqa: D401
    """Dispatch to the proper test function based on task type."""
    if task_name == "embedding":
        return _test_embedding(task_cfg)
    if task_name == "rerank":
        return _test_rerank(task_cfg)
    return _test_llm(task_name, task_cfg)


def main() -> None:  # noqa: D401
    cfg = load_config()
    tasks: Dict[str, Dict[str, Any]] = cfg.get("tasks", {})  # type: ignore[arg-type]

    # ------------------------------------------------------------------
    # Also load LLM service tasks defined in config/qa_pipeline.yaml
    # ------------------------------------------------------------------
    qa_cfg_path = PROJECT_ROOT / "config" / "qa_pipeline.yaml"
    if qa_cfg_path.exists():
        try:
            with qa_cfg_path.open("r", encoding="utf-8") as f:
                qa_data = yaml.safe_load(f) or {}

            # ``fixed_steps`` is a list of steps, each with ``name`` and ``config``
            for step in qa_data.get("fixed_steps", []):
                if not isinstance(step, dict):
                    continue
                name = step.get("name")
                if not isinstance(name, str):
                    continue  # invalid or missing name

                cfg_block = step.get("config", {})
                # Skip steps without provider/model (e.g. vector_recall)
                if not (isinstance(cfg_block, dict) and cfg_block.get("provider") and cfg_block.get("model")):
                    continue
                tasks[name] = cfg_block  # may overwrite existing – acceptable as we test latest definition
        except Exception as exc:  # noqa: BLE001
            console.print(f"[red]Failed loading qa_pipeline.yaml: {exc}")

    table = Table(title="Model Service Connectivity", box=box.SIMPLE_HEAD)
    table.add_column("Task", justify="left")
    table.add_column("Provider", justify="left")
    table.add_column("Model", justify="left")
    table.add_column("Status", justify="center")
    table.add_column("Latency (s)", justify="right")

    # Prepare tasks to run (skip those without provider/model unless special cases)
    runnable_tasks: Dict[str, Dict[str, Any]] = {}
    for task, tc in tasks.items():
        if task in ("embedding", "rerank"):
            runnable_tasks[task] = tc
        elif tc.get("provider") and tc.get("model"):
            runnable_tasks[task] = tc

    # Run concurrently
    rows = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(16, len(runnable_tasks))) as executor:
        future_to_task = {
            executor.submit(_run_task, t, cfg): (t, cfg) for t, cfg in runnable_tasks.items()
        }

        for future in concurrent.futures.as_completed(future_to_task):
            task, tc = future_to_task[future]
            try:
                status, latency = future.result()
            except Exception as exc:  # noqa: BLE001
                status, latency = f"❌ {exc}", 0.0

            # Immediate log output per task
            console.print(f"[cyan]{task}[/cyan] -> {status} ({latency:.2f}s)")

            rows.append((task, tc, status, latency))

    # Build final table
    for task, tc, status, latency in sorted(rows, key=lambda x: x[0]):
        table.add_row(task, str(tc.get("provider")), str(tc.get("model")), status, f"{latency:.2f}")

    console.print(Panel(table, title="[bold green]Model Services Test Summary", expand=False))


if __name__ == "__main__":  # pragma: no cover
    main() 