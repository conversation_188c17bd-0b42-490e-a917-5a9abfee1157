from __future__ import annotations

"""CI 脚本：校验组件 inputs/outputs 字段是否全部注册在 Field 枚举中。

用法::

    python scripts/check_fields.py  # 返回 0 表示校验通过

脚本逻辑：
1. 遍历 ``src/orchestration/components`` 下所有 ``*.py`` 文件；
2. 反射加载组件类，收集 ``inputs`` / ``outputs``;
3. 若字段是 ``Field`` 枚举成员则自动转换为字符串；
4. 若发现任意未注册的字符串字段，则打印错误并 exit 1。
"""

import importlib
import inspect
import pkgutil
import sys
from pathlib import Path
from types import ModuleType

from rich.console import Console  # type: ignore
from rich.table import Table

from src.orchestration.fields import Field

console = Console()

COMPONENT_PKG = "src.orchestration.components"


def _iter_component_modules() -> list[ModuleType]:
    """动态导入组件包下所有模块。"""
    pkg = importlib.import_module(COMPONENT_PKG)
    pkg_path = Path(pkg.__file__).parent  # type: ignore[attr-defined]
    modules = []
    for m in pkgutil.iter_modules([str(pkg_path)]):
        full_name = f"{COMPONENT_PKG}.{m.name}"
        modules.append(importlib.import_module(full_name))
    return modules


def _iter_components(modules: list[ModuleType]):
    """遍历模块，产出 (cls, file_path) 元组。"""
    for mod in modules:
        for _, obj in inspect.getmembers(mod, inspect.isclass):
            if getattr(obj, "name", None) and hasattr(obj, "inputs") and hasattr(obj, "outputs"):
                yield obj, Path(inspect.getfile(obj))


def main() -> None:  # noqa: D401
    modules = _iter_component_modules()

    unknown_fields: set[str] = set()

    table = Table("Component", "Field", "Location")

    for cls, path in _iter_components(modules):
        for lst_name in ("inputs", "outputs"):
            for f in getattr(cls, lst_name, []):
                # 枚举成员 -> 转为 str
                fname = str(f) if isinstance(f, Field) else str(f)
                if fname not in Field._value2member_map_:  # type: ignore[attr-defined]
                    unknown_fields.add(fname)
                    table.add_row(cls.name, fname, str(path))

    if unknown_fields:
        console.print("[red]❌ 字段未注册，详情如下：")
        console.print(table)
        console.print(f"共 {len(unknown_fields)} 个未注册字段。请在 src/orchestration/fields.py 中补充。")
        sys.exit(1)

    console.print("[green]✅ 字段校验通过，无未注册字段。")


if __name__ == "__main__":  # pragma: no cover
    main() 