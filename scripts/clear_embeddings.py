"""
一个用于清除所有现有嵌入和向量的脚本，以便重新开始。

该脚本主要执行两个操作:
1. 删除并重新创建 `vector_store` 目录，从而有效清除所有
   缓存的向量数据 (例如, from ChromaDB).
2. 连接到应用的数据库，并且对于每个包含
   'vectors' 列的表, 它将所有行的此列值设置为 NULL。

当您更新了嵌入模型或逻辑并需要从头开始重新生成所有嵌入时，此功能非常有用。

用法:
    python scripts/clear_embeddings.py
"""

import sys
import shutil
from pathlib import Path

from sqlalchemy import create_engine, inspect, text

# ------------------------------------------------------------------
# 确保在直接执行脚本时可以导入项目根目录
# ------------------------------------------------------------------
PROJECT_ROOT = Path(__file__).resolve().parent.parent
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))


def clear_vector_store():
    """
    删除并重新创建 vector_store 目录。
    """
    vector_store_path = PROJECT_ROOT / "vector_store"
    print(f"🧹 正在清理向量存储: {vector_store_path}")

    if vector_store_path.exists() and vector_store_path.is_dir():
        try:
            shutil.rmtree(vector_store_path)
            print(f"  - 已成功删除目录: {vector_store_path}")
        except OSError as e:
            print(f"  - 删除目录 {vector_store_path} 时出错: {e}")
            return

    try:
        vector_store_path.mkdir(exist_ok=True)
        print(f"  - 已成功创建目录: {vector_store_path}")
    except OSError as e:
        print(f"  - 创建目录 {vector_store_path} 时出错: {e}")


def clear_database_vectors():
    """
    连接到数据库并清除所有表中 'vectors' 列。
    """
    db_path = PROJECT_ROOT / "output/data.db"
    print(f"\n🧹 正在清理数据库中的 'vectors' 列: {db_path}")

    if not db_path.exists():
        print(f"  - 在 {db_path} 未找到数据库文件。跳过数据库清理。")
        return

    engine = create_engine(f"sqlite:///{db_path}")
    inspector = inspect(engine)

    try:
        with engine.connect() as connection:
            tables_with_vectors = []
            for table_name in inspector.get_table_names():
                columns = [c["name"] for c in inspector.get_columns(table_name)]
                if "vector" in columns:
                    tables_with_vectors.append(table_name)
            
            if not tables_with_vectors:
                print("  - 未找到包含 'vector' 列的表。")
                return

            transaction = connection.begin()
            try:
                for table_name in tables_with_vectors:
                    print(f"  - 正在清理表 '{table_name}' 中的 'vector' 列")
                    query = text(f'UPDATE "{table_name}" SET vector = NULL')
                    connection.execute(query)
                transaction.commit()
                print("  - 数据库更新已提交。")
            except Exception as e:
                print(f"  - 数据库更新期间发生错误: {e}")
                transaction.rollback()
                print("  - 事务已回滚。")
    except Exception as e:
        print(f"  - 无法连接或检查数据库: {e}")


def main():
    """主函数，用于编排清理过程。"""
    print("🚀 开始清理嵌入和向量...")
    clear_vector_store()
    clear_database_vectors()
    print("\n✅ 清理完成。")
    print("   您现在可以重新运行嵌入生成过程。")


if __name__ == "__main__":
    main() 