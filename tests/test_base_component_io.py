import pytest

from src.orchestration.base_component import BaseComponent
from src.orchestration.fields import Field


class _DummyComponent(BaseComponent):
    """内部测试用组件，不注册到全局 Registry。"""

    name = "_dummy_component"
    # 代码端默认输入/输出
    inputs = [Field.query]
    outputs = [Field.answer]

    # 简易 run：原样返回 payload，便于实例化
    def run(self, payload, **kwargs):  # noqa: D401
        return payload


def test_apply_io_config_match() -> None:
    """当 YAML 配置与代码声明一致时，应通过校验。"""

    comp = _DummyComponent()

    # 与代码端一致的 inputs/outputs
    comp.apply_io_config(inputs=[Field.query], outputs=[Field.answer])

    # 最终属性应保持一致
    assert list(comp.inputs) == [str(Field.query)]  # 类型已转字符串
    assert list(comp.outputs) == [str(Field.answer)]


def test_apply_io_config_mismatch() -> None:
    """当 YAML 配置与代码声明冲突时，必须抛出 ValueError。"""

    comp = _DummyComponent()

    with pytest.raises(ValueError):
        # 故意提供不一致的 inputs
        comp.apply_io_config(inputs=[Field.intent], outputs=[Field.answer]) 