import builtins
import json
from pathlib import Path
from typing import Any, Dict

import pytest

# ------------------------------------------------------------
# 工具函数 & 假数据
# ------------------------------------------------------------

TEST_PROMPT = (
    "你是测试用的提示。数据项列表: [{item_list}]。用户问题: {question}"  # 占位符保持与生产一致
)

@pytest.fixture(autouse=True)
def patch_llm(monkeypatch):
    """全局 monkeypatch ask() 与 summarize_answer()，避免真实调用 LLM 服务。"""

    # --- mock src.parser.llm.client.ask ---------------------------------
    def _fake_ask(prompt: str, *args: Any, **kwargs: Any) -> str:  # noqa: D401
        # 简单根据 prompt 返回固定 JSON
        return json.dumps({"data_item": "证件号码", "confidence": 1.0})

    monkeypatch.setattr("src.parser.llm.client.ask", _fake_ask, raising=False)

    # --- mock llm_answer_component.summarize_answer ---------------------
    def _fake_sum(question: str, context: str, **_: Any) -> str:  # noqa: D401
        return "证件号码格式：字符型，最长 70 位。"

    monkeypatch.setattr(
        "src.orchestration.components.llm_answer_component.summarize_answer",
        _fake_sum,
        raising=False,
    )

    yield

# ------------------------------------------------------------
# 测试 DataItemIdentifier 独立运行
# ------------------------------------------------------------


def test_data_item_identifier_identify():  # noqa: D401
    from src.orchestration.components.data_item_identifier import DataItemIdentifier

    comp = DataItemIdentifier({"prompt": TEST_PROMPT, "provider": "fake", "model": "fake"})

    state: Dict[str, Any] = {
        "query": "员工表中的证件号码格式是什么?",
        "selected_table": "员工表",
        "file_name": "CBRC_EAST5.0_业务Mapping_V2.2.4.xlsx",
    }

    out = comp.run(state)

    assert out["identified_data_item"] == "证件号码"
    assert pytest.approx(out["confidence"], 0.001) == 1.0

    # 能否拿到完整行
    row = comp.get_full_row_for_item(state["file_name"], state["selected_table"], "证件号码")
    assert row is not None and "数据项名称" in row and row["数据项名称"] == "证件号码"

# ------------------------------------------------------------
# 测试整个 Pipeline 的快捷通道
# ------------------------------------------------------------


def test_pipeline_fast_path(monkeypatch):  # noqa: D401
    # 1) 构建流水线
    from src.orchestration.pipeline import MixedPipeline

    pipe = MixedPipeline("config/qa_pipeline.yaml")
    pipe.build("员工表中的证件号码格式是什么?")

    # 2) monkeypatch 数据项识别器，使其直接返回高置信度且跳过 ask()
    from src.orchestration.components.data_item_identifier import DataItemIdentifier
    from src.orchestration.components.sheet_name_classifier_component import SheetNameClassifierComponent as SheetNameClassifier

    def _fake_ident(self, q, items):  # noqa: D401
        return "证件号码", 1.0

    monkeypatch.setattr(DataItemIdentifier, "_identify_item_with_llm", _fake_ident, raising=True)

    def _fake_sheet(self, state):  # noqa: D401
        state.update({"selected_table": "员工表", "sheet_ids": ["sheet:9"]})
        return state

    monkeypatch.setattr(SheetNameClassifier, "run", _fake_sheet, raising=True)

    res = pipe.execute({"query": "员工表中的证件号码格式是什么?"})

    # 断言：流水线应直接生成 answer，且 row_vector_recall 不会运行（row_texts 长度为 1）
    assert "answer" in res and res["answer"]
    assert res["row_texts"] and len(res["row_texts"]) == 1 