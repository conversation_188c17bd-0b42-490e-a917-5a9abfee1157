import sys
from pathlib import Path as _P

# 确保项目根目录加入 sys.path，避免 "import src" 失败
ROOT = _P(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

import tempfile
from pathlib import Path

import pandas as pd  # type: ignore
import pytest  # type: ignore

from src.ingestion.excel_parser import ExcelParser
from src.ingestion.pipeline import Pipeline


class DummyVectorStore:  # noqa: D101
    def add_embeddings(self, ids, embeddings, metadata):  # noqa: D401
        pass

    def query(self, embedding, top_k=10, filter=None):  # noqa: D401
        return []

    def delete(self, ids):  # noqa: D401
        pass


class DummyVectorizer:
    def __init__(self) -> None:  # noqa: D401
        pass

    def embed_texts(self, texts):  # type: ignore[override]
        return [[0.0] * 3 for _ in texts]


def _create_sample_excel(tmp_dir: Path) -> Path:
    df = pd.DataFrame({
        "姓名": ["张三", "李四"],
        "部门": ["销售", "市场"],
        "金额": [1250.5, 980.0],
        "备注": ["该客户为VIP", "潜在大单"],
    })
    file_path = tmp_dir / "sample.xlsx"
    with pd.ExcelWriter(file_path, engine="openpyxl") as writer:  # type: ignore
        df.to_excel(writer, index=False, sheet_name="Sheet1")
    return file_path


@pytest.fixture()
def temp_excel(tmp_path: Path):
    return _create_sample_excel(tmp_path)


def test_excel_parser_basic(temp_excel: Path):
    parser = ExcelParser(temp_excel.parent)
    parsed_tables = parser.parse_excel(temp_excel)
    assert parsed_tables, "Parser 应至少返回一个表"
    pt = parsed_tables[0]
    assert "姓名" in pt.dataframe.columns
    assert len(pt.dataframe) == 2


def test_pipeline_end_to_end(temp_excel: Path, tmp_path: Path):
    db_path = tmp_path / "test.db"
    pipeline = Pipeline(
        root_dir=temp_excel.parent,
        db_path=db_path,
        vectorizer=DummyVectorizer(),  # type: ignore[arg-type]
        vector_store=DummyVectorStore(),  # type: ignore[arg-type]
    )
    pipeline.run(excel_files=[temp_excel])

    # 校验 SQLite 中是否存在数据表及向量表
    import sqlite3

    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = {row[0] for row in cur.fetchall()}
    # 寻找 Sheet 表
    sheet_table = next(t for t in tables if t not in {"file_metadata", "sheet_metadata", "sqlite_sequence", "row_vectors"})
    assert sheet_table in tables
    assert "row_vectors" in tables

    # 检查 row_vectors 中是否有对应表的数据（可能为空）
    cur.execute("SELECT COUNT(*) FROM row_vectors WHERE table_name = ?", (sheet_table,))
    count = cur.fetchone()[0]
    assert count >= 0
    conn.close() 