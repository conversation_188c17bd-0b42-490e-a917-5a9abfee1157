import sys
from pathlib import Path as _P

ROOT = _P(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

import json
import sqlite3
import types
from typing import List

import pytest  # type: ignore
import requests

from src.vectorization.vectorizer import Vectorizer
from src.ingestion.pipeline import Pipeline
from src.ingestion.excel_parser import ExcelParser
import pandas as pd  # type: ignore


class DummyVectorStore:  # noqa: D101
    def add_embeddings(self, ids, embeddings, metadata):
        pass

    def query(self, embedding, top_k=10, filter=None):
        return []

    def delete(self, ids):
        pass


class FailNTimes:
    """helper to simulate request failures then success"""

    def __init__(self, n_fail: int):
        self.n_fail = n_fail
        self.calls = 0

    def __call__(self, *args, **kwargs):  # noqa: D401
        self.calls += 1
        if self.calls <= self.n_fail:
            raise requests.RequestException("simulated network error")

        # success response stub
        class _Resp:  # noqa: D101
            status_code = 200

            def raise_for_status(self):
                pass

            def json(self):  # noqa: D401
                return {"embedding": [0.1, 0.2, 0.3]}

        return _Resp()


@pytest.fixture()
def monkey_sleep(monkeypatch):
    monkeypatch.setattr("time.sleep", lambda x: None)


def test_vectorizer_retry(monkeypatch, monkey_sleep):
    failer = FailNTimes(2)
    monkeypatch.setattr(requests, "post", failer)

    v = Vectorizer(max_retries=3, backoff_factor=0)  # backoff_factor=0 避免 sleep
    emb = v.embed_texts(["hello"])[0]
    assert len(emb) == 3
    assert failer.calls == 3  # 2 fail + 1 success


def _build_excel(tmp_dir: _P) -> _P:
    df = pd.DataFrame({"text": ["a", "b"]})
    fp = tmp_dir / "err.xlsx"
    with pd.ExcelWriter(fp, engine="openpyxl") as wr:  # type: ignore
        df.to_excel(wr, index=False)
    return fp


def test_pipeline_skip_failed_embeddings(monkeypatch, monkey_sleep, tmp_path: _P):
    # requests.post 始终失败
    monkeypatch.setattr(requests, "post", lambda *a, **k: (_ for _ in ()).throw(requests.RequestException))

    excel_path = _build_excel(tmp_path)
    db_path = tmp_path / "err.db"

    pipeline = Pipeline(root_dir=tmp_path, db_path=db_path, vectorizer=Vectorizer(max_retries=2, backoff_factor=0), vector_store=DummyVectorStore())  # type: ignore[arg-type]
    pipeline.run(excel_files=[excel_path])

    con = sqlite3.connect(db_path)
    cur = con.cursor()
    tbl = "err_sheet1"
    cur.execute(f"SELECT COUNT(*) FROM {tbl}")
    assert cur.fetchone()[0] == 2  # 行数据仍写入

    cur.execute("SELECT COUNT(*) FROM row_vectors WHERE table_name = ?", (tbl,))
    assert cur.fetchone()[0] == 0  # 向量失败被跳过
    con.close() 