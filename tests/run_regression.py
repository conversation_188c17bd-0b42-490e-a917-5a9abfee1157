from __future__ import annotations
import argparse
import json
import subprocess
import sys
import time
import logging
from pathlib import Path
from typing import Dict, Any, List
"""Run full regression of LLM experiments (T1~T6 + perf) in sequence.

Usage:
    python -m tests.run_regression --file data_files/CBRC_EAST5.0_业务Mapping_V2.2.4.xlsx --sheet 0

The script will:
 1. Resolve sheet name (index allowed)
 2. Execute each experiment via in-process function calls or subprocess
 3. Collect outputs into `output/regression_{timestamp}.json` for later diff
 4. Exit with non-zero status if any experiment raises.
"""

logging.basicConfig(level=logging.INFO)

EXPERIMENT_MODULES = [
    "src.experiments.llm_probe",
    "src.experiments.llm_field_mapping",
    "src.experiments.llm_doc_extract",
    "src.experiments.llm_anomaly",
    "src.experiments.llm_header_merge",
    "src.experiments.llm_perf",
]


def run_module(module_name: str, params: List[str]) -> str:
    """Run module as subprocess to keep isolation and capture stdout."""
    cmd = [sys.executable, "-m", module_name] + params
    logging.info("Running %s", " ".join(cmd))
    proc = subprocess.run(cmd, capture_output=True, text=True)
    if proc.returncode != 0:
        logging.error("Module %s failed: %s", module_name, proc.stderr.strip())
        raise RuntimeError(f"Experiment {module_name} failed")
    return proc.stdout.strip()


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--file", type=Path, required=True)
    ap.add_argument("--sheet", type=str, default="0", help="Sheet name or index (default 0)")
    ap.add_argument("--out", type=Path, default=Path("output"))
    args = ap.parse_args()

    # ensure output dir
    args.out.mkdir(parents=True, exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    outfile = args.out / f"regression_{timestamp}.json"

    # Determine sheet name if index
    import openpyxl
    wb = openpyxl.load_workbook(args.file, read_only=True, data_only=True)
    sheet_name = args.sheet
    if sheet_name.isdigit():
        idx = int(sheet_name)
        sheet_name = wb.sheetnames[idx]
    logging.info("Using sheet: %s", sheet_name)

    results: Dict[str, Any] = {
        "file": str(args.file),
        "sheet": sheet_name,
        "timestamp": timestamp,
        "experiments": {},
    }

    for mod in EXPERIMENT_MODULES:
        name = mod.split(".")[-1]
        try:
            if mod == "src.experiments.llm_perf":
                # perf script does not rely on sheet param
                out = run_module(mod, ["--output", str(args.out / "perf_tmp.csv")])
            elif mod == "src.experiments.llm_probe":
                out = run_module(mod, ["--file", str(args.file), "--sheet", sheet_name])
            else:
                out = run_module(mod, ["--file", str(args.file), "--sheet", sheet_name])
            results["experiments"][name] = out
        except Exception as e:
            results["experiments"][name] = {"error": str(e)}
            logging.exception("Experiment %s failed", name)

    # write consolidated json
    with outfile.open("w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"Regression output saved to {outfile}")

    # If any experiment has error, exit with 1
    errors = [v for v in results["experiments"].values() if isinstance(v, dict) and "error" in v]
    if errors:
        sys.exit(1)


if __name__ == "__main__":
    main() 