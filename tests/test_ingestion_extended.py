import sys
from pathlib import Path as _P

# 确保项目根目录在 sys.path
ROOT = _P(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

import sqlite3
import pandas as pd  # type: ignore
import pytest  # type: ignore

from src.ingestion.excel_parser import ExcelParser
from src.storage.db import DBWriter
from src.ingestion.pipeline import Pipeline


class DummyVectorizer:
    """简化向量器：返回固定零向量。"""

    def embed_texts(self, texts):  # type: ignore
        return [[0.0] * 3 for _ in texts]


class DummyVectorStore:  # noqa: D101
    def add_embeddings(self, ids, embeddings, metadata):
        pass

    def query(self, embedding, top_k=10, filter=None):
        return []

    def delete(self, ids):
        pass


# ---------------------------------------------------------------------------
# Helper to build complex Excel file
# ---------------------------------------------------------------------------

def _build_multi_sheet_excel(tmp_dir: _P) -> _P:
    fp = tmp_dir / "multi.xlsx"
    df1 = pd.DataFrame({
        "col1": [1, 2, 3],
        "text": ["hello world" * 2, "foo bar" * 3, "baz" * 4],
    })
    df2 = pd.DataFrame({
        "name": ["Alice", "Bob"],
        "dept": ["HR", "Eng"],
        "note": ["note1", "note2"],
    })
    with pd.ExcelWriter(fp, engine="openpyxl") as writer:  # type: ignore
        df1.to_excel(writer, index=False, sheet_name="SheetA")
        df2.to_excel(writer, index=False, sheet_name="SheetB")
    return fp


# ---------------------------------------------------------------------------
# Tests
# ---------------------------------------------------------------------------


def test_parser_multi_sheet(tmp_path: _P):
    excel_path = _build_multi_sheet_excel(tmp_path)
    parser = ExcelParser(tmp_path)
    tables = parser.parse_excel(excel_path)
    names = {t.meta["sheet_name"] for t in tables}
    assert names == {"SheetA", "SheetB"}
    # 列名已标准化为小写+下划线
    for t in tables:
        assert all(isinstance(c, str) for c in t.dataframe.columns)


def test_dbwriter_append_replace(tmp_path: _P):
    db_path = tmp_path / "test.db"
    writer = DBWriter(db_path)

    df = pd.DataFrame({"a": [1, 2], "b": ["x", "y"]})
    from src.ingestion.excel_parser import ParsedTable

    pt = ParsedTable(name="tbl", dataframe=df, meta={})

    # 初次写入
    writer.write_table(pt, if_exists="fail")

    # append 追加
    writer.write_table(pt, if_exists="append")

    con = sqlite3.connect(db_path)
    cur = con.cursor()
    cur.execute("SELECT COUNT(*) FROM tbl")
    assert cur.fetchone()[0] == 4

    # replace 覆盖
    writer.write_table(pt, if_exists="replace")
    cur.execute("SELECT COUNT(*) FROM tbl")
    assert cur.fetchone()[0] == 2
    con.close()


def test_metadata_upsert_idempotent(tmp_path: _P):
    db = DBWriter(tmp_path / "m.db")
    id1 = db.upsert_file_metadata("/path/file.xlsx")
    id2 = db.upsert_file_metadata("/path/file.xlsx")
    assert id1 == id2
    sheet1 = db.upsert_sheet_metadata(id1, "sheet1")
    sheet2 = db.upsert_sheet_metadata(id1, "sheet1")
    assert sheet1 == sheet2


def test_pipeline_multiple_runs(tmp_path: _P):
    excel_path = _build_multi_sheet_excel(tmp_path)
    db_path = tmp_path / "pipe.db"

    pipeline = Pipeline(root_dir=tmp_path, db_path=db_path, vectorizer=DummyVectorizer(), vector_store=DummyVectorStore())  # type: ignore[arg-type]

    # run twice without overwrite
    pipeline.run(excel_files=[excel_path])
    pipeline.run(excel_files=[excel_path])

    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # file_metadata 应仅 1 条
    cur.execute("SELECT COUNT(*) FROM file_metadata")
    assert cur.fetchone()[0] == 1
    # sheet_metadata 也应保持 2 条
    cur.execute("SELECT COUNT(*) FROM sheet_metadata")
    assert cur.fetchone()[0] == 2
    conn.close() 