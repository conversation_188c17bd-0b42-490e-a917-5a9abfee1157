# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
这是一个金融监管层次化数据服务项目，为LLM QA提供层次化数据管理与服务平台。项目使用SQLite存储结构化数据，Chroma存储向量数据，通过FastAPI提供MCP/OpenAPI接口。

## Architecture
- **API Layer**: FastAPI with OpenAPI/MCP specification
- **Service Layer**: Core business logic in `src/orchestration/`
- **Storage Layer**: SQLite (metadata) + Chroma (vectors)
- **Data Pipeline**: Two-stage process - Excel ingestion → vectorization

## Key Components Hierarchy
```
file_metadata (报表文件)
├── sheet_metadata (表格/Sheet)
│   ├── row_vectors (字段数据)
│   └── sheet_attributes (表格属性)
└── file_attributes (文件属性)
```

## Development Commands

### Data Pipeline
```bash
# Two-stage data ingestion
python -m src.ingestion --root data_files --db output/data.db --no-vector
python -m src.vectorization.backfill_vectors --db output/data.db --sync --persist vector_store

# Reset data
python scripts/clear_embeddings.py
```

### Testing
```bash
# Run all tests
pytest tests/

# Run specific test categories
pytest tests/test_ingestion.py
pytest tests/test_fast_path.py
```

### Server & API
```bash
# Start FastAPI server
uvicorn src.server:app --reload

# QA pipeline testing
python -m src.qa.pipeline

# Model connectivity test
python scripts/test_model_services.py
```

### Database Management
```bash
# Rebuild FTS index
python scripts/rebuild_fts.py

# Check database schema
python scripts/analyze_schema.py
```

## Configuration Files
- `config/app_settings.yaml` - Service configuration
- `config/qa_pipeline.yaml` - QA pipeline components
- `config/intent_rules.yaml` - Intent classification rules

## Code Structure
- `src/orchestration/components/` - QA pipeline components (intent classifier, vector recall, LLM answer)
- `src/ingestion/` - Excel parsing and data ingestion
- `src/vectorization/` - Vector generation and Chroma synchronization
- `src/storage/` - Database models and operations
- `src/intent/` - Intent classification logic

## Development Patterns
- All SQL operations use SQLAlchemy ORM
- Vector operations through Chroma with custom VectorStore wrapper
- Pipeline architecture based on configurable components
- Async operations for embedding generation and LLM calls