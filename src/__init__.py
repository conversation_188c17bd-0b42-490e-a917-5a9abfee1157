# ---------------------------------------------------------------------------
# Global proxy disable: ensure any `requests` call inside the project will NOT
# inadvertently use system-wide HTTP/SOCKS proxies (which may introduce
# PySocks dependency issues).  This executes once when the `src` package is
# imported, before other sub-modules make outbound HTTP calls.
# ---------------------------------------------------------------------------

import os
from typing import Final

# Remove common proxy environment variables
_PROXY_VARS: Final = (
    "HTTP_PROXY",
    "http_proxy",
    "HTTPS_PROXY",
    "https_proxy",
    "ALL_PROXY",
    "all_proxy",
)

for _var in _PROXY_VARS:
    os.environ.pop(_var, None)

# Explicitly tell `requests` to skip proxies by default
os.environ.setdefault("NO_PROXY", "*")

# Package version placeholder (optional)
__version__ = "0.1.0" 