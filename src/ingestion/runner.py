from __future__ import annotations

"""Rich command-line interface for the Excel ingestion pipeline.

This module replicates **exactly** the functionality of the legacy
`run_pipeline.py` helper (progress bars, Ollama health-check, rich
logging, skip-vector mode, etc.) but lives inside the `src` package so
that the unified entry point becomes::

    python -m src.ingestion --root data_files --db output/data.db --overwrite

The old top-level script still works; it now simply re-exports this
``runner`` module to avoid code duplication.
"""

import argparse
import logging
import os
import warnings
from pathlib import Path
from typing import Optional

import pandas as pd  # type: ignore
import requests
from rich.console import Console
from rich.logging import RichHandler
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeElapsedColumn,
)

# NOTE: use *relative* imports so that the package can be relocated easily
from .pipeline import Pipeline, ExcelParser  # noqa: E402
from ..vectorization.vectorizer import Vectorizer  # noqa: E402
from ..utils.config import load_config  # noqa: E402
from ..utils.logger import setup_logging  # noqa: E402

# We import lazily to avoid chromadb dependency when vector disabled
try:
    from ..vectorization.vector_store import ChromaVectorStore  # type: ignore  # noqa: E402
except Exception:  # pragma: no cover
    ChromaVectorStore = None  # type: ignore  # noqa: N816


class DummyVectorizer:  # noqa: D101
    """Placeholder Vectorizer used when --no-vector is specified."""

    def embed_texts(self, texts):  # type: ignore[override]
        return [[] for _ in texts]

    def attach_progress(self, progress):  # noqa: D401, ANN001
        """No-op, keeps API the same as :class:`~Vectorizer`."""
        pass


class LoggingVectorizer(Vectorizer):  # noqa: D101
    """Vectorizer that reports its progress to a Rich progress bar."""

    def __init__(self, console: Console, *args, **kwargs):  # type: ignore[no-untyped-def]
        super().__init__(*args, **kwargs)
        self._console = console
        self._progress: Optional[Progress] = None

    # ------------------------------------------------------------------
    def attach_progress(self, progress: Progress) -> None:  # noqa: D401
        self._progress = progress

    # ------------------------------------------------------------------
    def embed_texts(self, texts):  # type: ignore[override]
        if not texts:
            return []

        task_id = None
        if self._progress is not None:
            task_id = self._progress.add_task("[magenta]Embedding", total=len(texts))

        embeddings: list[list[float]] = []
        for text in texts:
            res = super().embed_texts([text])[0]
            embeddings.append(res)
            if task_id is not None and self._progress is not None:
                self._progress.advance(task_id)

        if task_id is not None and self._progress is not None:
            self._progress.remove_task(task_id)
        return embeddings


class DummyVectorStore:  # noqa: D101
    """Placeholder VectorStore used when embeddings are disabled."""

    def add_embeddings(self, ids, embeddings, metadata):  # noqa: D401
        pass

    def query(self, embedding, top_k: int = 10, filter=None):  # noqa: D401
        return []

    def delete(self, ids):  # noqa: D401
        pass


# ----------------------------------------------------------------------
# CLI helpers
# ----------------------------------------------------------------------

def _parse_args() -> argparse.Namespace:  # noqa: D401
    p = argparse.ArgumentParser("Run Excel ingestion pipeline")
    p.add_argument("--root", type=Path, default=Path("data_files"), help="Excel directory root")
    p.add_argument("--db", type=Path, default=Path("output/data.db"), help="SQLite DB path")
    p.add_argument("--overwrite", action="store_true", help="Replace existing tables")

    vector_grp = p.add_mutually_exclusive_group()
    vector_grp.add_argument("--vector", dest="vector", action="store_true", help="Generate embeddings")
    vector_grp.add_argument("--no-vector", dest="vector", action="store_false", help="Skip embeddings")
    p.set_defaults(vector=True)
    return p.parse_args()


# ----------------------------------------------------------------------
# Main entry
# ----------------------------------------------------------------------

def main() -> None:  # noqa: D401
    """Run the ingestion pipeline with Rich UI."""

    args = _parse_args()

    console = Console()
    os.makedirs("output", exist_ok=True)

    # ------------------------------------------------------------------
    # Logging setup (plain stream + file)
    # ------------------------------------------------------------------
    stream_handler = RichHandler(console=console, show_time=False, show_path=False, markup=True)
    stream_handler.setLevel(logging.INFO)

    # Use shared logging utility to write detailed logs under logs/ directory
    setup_logging(log_file="logs/ingestion_pipeline.log", level=logging.DEBUG)

    # 移除可能已存在的普通 StreamHandler，避免与 RichHandler 重复输出
    root_logger = logging.getLogger()
    for h in list(root_logger.handlers):
        if isinstance(h, logging.StreamHandler) and not isinstance(h, (logging.FileHandler, RichHandler)):
            root_logger.removeHandler(h)

    if not any(isinstance(h, RichHandler) for h in root_logger.handlers):
        root_logger.addHandler(stream_handler)

    warnings.filterwarnings("ignore", category=FutureWarning)

    # ------------------------------------------------------------------
    # Vectorizer / VectorStore selection & Ollama health check
    # ------------------------------------------------------------------
    if args.vector:
        # ------------------------------------------------------------------
        # Determine Ollama host (explicit env var > new config > legacy)      
        # ------------------------------------------------------------------
        ollama_host = os.getenv("OLLAMA_HOST")
        if ollama_host is None:
            cfg = load_config()
            ollama_host = (
                # New style config (see config/app_settings.yaml)
                cfg.get("llm_services", {})  # type: ignore[arg-type]
                .get("ollama", {})
                .get("host")
            ) or (
                # Legacy flat keys kept for backward-compat
                cfg.get("ollama_host")
                or cfg.get("llm", {}).get("ollama_host")
            ) or "http://localhost:11434"
        ollama_host = ollama_host.rstrip("/")

        try:
            # Skip proxies to avoid SOCKS schema errors when system proxy is set
            requests.get(ollama_host, timeout=2, proxies={"http": None, "https": None})  # type: ignore[arg-type]
        except Exception as exc:  # noqa: BLE001
            logging.error("⚠️  Ollama 服务不可用 (%s)，已自动降级为 --no-vector 模式 (host=%s)", exc, ollama_host)
            logging.debug("Ollama health check exception", exc_info=exc)
            args.vector = False

    vectorizer = LoggingVectorizer(console) if args.vector else DummyVectorizer()  # type: ignore[arg-type]
    if args.vector and ChromaVectorStore is not None:
        vector_store = ChromaVectorStore()
    else:
        vector_store = DummyVectorStore()

    # ------------------------------------------------------------------
    console.rule("[bold cyan]Excel Ingestion Pipeline")

    parser = ExcelParser(args.root)
    excel_files = parser.discover_excels()

    # Pre-count total sheets for global progress
    file_sheet_counts: list[tuple[Path, int]] = []
    for fp in excel_files:
        try:
            xl = pd.ExcelFile(fp, engine="openpyxl")  # type: ignore
            file_sheet_counts.append((fp, len(xl.sheet_names)))
        except Exception:
            file_sheet_counts.append((fp, 0))

    total_sheets = sum(cnt for _, cnt in file_sheet_counts)

    with Progress(
        SpinnerColumn(),
        TextColumn("{task.description}"),
        BarColumn(),
        MofNCompleteColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        file_task = progress.add_task("[green]Files", total=len(excel_files))
        sheet_task = progress.add_task("[blue]Sheets", total=total_sheets)

        pipeline = Pipeline(
            root_dir=args.root,
            db_path=args.db,
            vectorizer=vectorizer,  # type: ignore[arg-type]
            vector_store=vector_store,  # type: ignore[arg-type]
        )

        if args.vector:
            vectorizer.attach_progress(progress)

        pipeline.attach_sheet_progress(progress, sheet_task)

        for i, excel_path in enumerate(excel_files):
            progress.update(file_task, description=f"[cyan]Parsing [bold]{excel_path.name}")
            try:
                pipeline.run(excel_files=[excel_path], overwrite=args.overwrite and i == 0)
                progress.console.log(f"[bold green]✓ {excel_path.name} processed")
            except Exception as exc:  # noqa: BLE001
                progress.console.log(f"[bold red]✗ {excel_path.name} failed: {exc}")
            progress.advance(file_task)

    console.rule("[bold green]Pipeline completed")


if __name__ == "__main__":  # pragma: no cover
    main() 