from __future__ import annotations
from typing import List

import logging
import re

import pandas as pd  # type: ignore

from ..parser.llm.client import ask, LLMClientError
from ..utils.config import get_task_cfg


"""简单摘要生成。
若无 LLM，可使用模板拼接列名、行数、示例行。
"""

logger = logging.getLogger(__name__)


# ------------------------------------------------------------------
# Helper utilities
# ------------------------------------------------------------------


_THINK_PATTERN = re.compile(r"<think>.*?</think>", re.S)


def _strip_think(text: str) -> str:  # noqa: D401
    """Remove <think> ... </think> blocks as well as standalone '<think>' lines."""
    # 1) Remove well-formed <think>...</think> blocks
    cleaned = _THINK_PATTERN.sub("", text)

    # 2) Remove any residual single-line '<think>' markers
    cleaned_lines = [ln for ln in cleaned.splitlines() if "<think>" not in ln.lower()]
    return "\n".join(cleaned_lines).strip()


def _postprocess_summary(text: str, max_chars: int) -> str:  # noqa: D401
    """截断长度，确保不超过最大字符数。"""
    # 1) Remove LLM 思考内容
    text = _strip_think(text)

    # 2) 去除 Markdown 代码块包裹（```）
    if text.startswith("```"):
        # 仅保留首尾代码围栏中的内容
        text = re.sub(r"^```[a-zA-Z0-9]*", "", text).strip()
        text = re.sub(r"```$", "", text).strip()

    # 3) 截断到指定长度
    if len(text) > max_chars:
        text = text[: max_chars].rstrip()
    return text


def summarize_sheet(sheet_name: str, df: pd.DataFrame, max_cols: int = 5) -> str:  # noqa: D401
    """调用大模型生成 Sheet 级描述；失败时退回模板。"""
    cols_all = list(df.columns)
    cols = cols_all[:max_cols]

    # ---- 构造示例字段值 & 数据行 -------------------------------------------------
    sample_dict: dict[str, str] = {}
    if not df.empty:
        first_row = df.iloc[0]
        for col in cols:
            val = first_row[col] if col in first_row else ""
            sample_dict[col] = str(val)[:20]  # 取前 20 字符，避免 prompt 过长

    sample_str = "; ".join(f"{k}={v}" for k, v in sample_dict.items())

    # Build up to 3 sample rows (truncated values to avoid overly long prompts)
    MAX_SAMPLE_ROWS = 3
    MAX_VAL_LEN = 20
    sample_rows_lines: list[str] = []
    for idx, (_, row) in enumerate(df.head(MAX_SAMPLE_ROWS).iterrows()):
        cells = [f"{col}={str(row[col])[:MAX_VAL_LEN]}" for col in cols_all]
        sample_rows_lines.append(f"行{idx+1}: " + "; ".join(cells))
    sample_rows_str = " | ".join(sample_rows_lines)

    # 强制重新加载配置，避免使用缓存的旧值
    _sheet_cfg = get_task_cfg("sheet_summary", force_reload=True)
    max_words = int(_sheet_cfg.get("max_words", 30))
    system_msg = _sheet_cfg.get(
        "prompt",
        "你是一位中文数据分析助手。请用一句中文总结此表格的主要内容与用途，直接给出答案，不要输出思考过程。",
    )

    prompt = (
        f"表名：{sheet_name}\n"
        f"总行数：{len(df)}\n"
        f"列名：{', '.join(map(str, cols_all))}\n"
        f"字段示例：{sample_str if sample_str else ', '.join(map(str, cols[:max_cols]))}\n"
        f"示例行：{sample_rows_str}\n"
        f"总结该表的内容与用途，直接给出一句中文描述。"
    )

    try:
        # ---- 调用大模型 ------------------------------------------------------
        raw = ask(
            prompt,
            system=system_msg,
            provider=_sheet_cfg.get("provider"),
            model=_sheet_cfg.get("model"),
            temperature=0.1,
            max_tokens=4096,
            think=False,
        ).strip()

        # Post-process: remove <think> etc., truncate
        desc = _postprocess_summary(raw, max_words)
        if not desc:
            raise ValueError("empty summary")
        return desc
    except (LLMClientError, ValueError) as ex:  # noqa: BLE001
        logger.warning("LLM summarization (sheet) failed: %s", ex)
        # 回退到原模板
        fallback = (
            f"{sheet_name}：{', '.join(map(str, cols_all[:10]))} 等 {len(df)} 行结构化数据。"
        )
        return _postprocess_summary(fallback, max_words)


def summarize_file(file_name: str, sheet_summaries: List[str]) -> str:  # noqa: D401
    """调用大模型生成文件级描述；失败时退回模板。"""
    # 强制重新加载配置，避免使用缓存的旧值
    _file_cfg = get_task_cfg("file_summary", force_reload=True)
    max_words = int(_file_cfg.get("max_words", 50))
    joined = " | ".join(sheet_summaries[:3])

    # 补充 sheet 名称列表，便于大模型把握上下文
    sheet_names: List[str] = []
    for s in sheet_summaries:
        # 期望格式为 "<sheet_name>：..."
        if "：" in s:
            sheet_names.append(s.split("：", 1)[0])
        else:
            sheet_names.append(s.split(":", 1)[0])
    sheet_list_str = ", ".join(sheet_names)

    system_msg = _file_cfg.get(
        "prompt",
        "你是一位中文数据分析助手。请用一句中文总结此 Excel 文件的主要内容与用途，直接给出答案，不要输出思考过程。",
    )

    prompt = (
        f"文件名：{file_name}\n"
        f"包含 Sheet 数：{len(sheet_summaries)}\n"
        f"Sheet 列表：{sheet_list_str}\n"
        f"主要 Sheet 描述示例：{joined}\n"
        f"总结该文件的内容与用途，直接给出一句中文描述。"
    )

    try:
        raw = ask(
            prompt,
            system=system_msg,
            provider=_file_cfg.get("provider"),
            model=_file_cfg.get("model"),
            temperature=0.1,
            max_tokens=4096,
            think=False,
        ).strip()

        desc = _postprocess_summary(raw, max_words)
        if not desc:
            raise ValueError("empty summary")
        return desc
    except (LLMClientError, ValueError) as ex:  # noqa: BLE001
        logger.warning("LLM summarization (file) failed: %s", ex)
        fallback = (
            f"{file_name}：包含 {len(sheet_summaries)} 个工作表，主题概述：{joined}"
        )
        return _postprocess_summary(fallback, max_words)


def summarize_table(table_name: str, df: pd.DataFrame, max_cols: int = 5) -> str:  # noqa: D401
    """生成表格块描述文本，用于向量化。

    Parameters
    ----------
    table_name : str
        逻辑表名（文件_工作表_子表索引）。
    df : pandas.DataFrame
        表格数据。
    max_cols : int, optional
        打印多少列名示例，默认 5。
    """
    cols = list(df.columns)[:max_cols]
    desc = (
        f"表格块：{table_name}，行数：{len(df)}，列名示例：{', '.join(map(str, cols))}。"
        "该表块包含结构化数据，可用于精确检索与分析。"
    )
    return desc 