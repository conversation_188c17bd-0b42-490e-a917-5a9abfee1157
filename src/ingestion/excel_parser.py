from __future__ import annotations
import json
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import List, Dict, Tuple, Any, Iterator

import pandas as pd  # type: ignore
import openpyxl  # type: ignore

# 配置加载
from ..utils.config import get_task_cfg

from ..parser.llm.client import ask, LLMClientError, _extract_json_block  # type: ignore

"""
全新 ExcelParser：
1. 彻底取消合并单元格并复制值；
2. 使用 LLM（通过 parser.llm.client.ask）分析前 10 行，识别表头行与数据起始行；
3. 支持多级表头拼接，自动去重、清洗列名；
4. 输出 ParsedTable 列表，供后续入库与向量化使用。
"""


__all__ = [
    "ParsedTable",
    "ExcelParser",
]

logger = logging.getLogger(__name__)


# ---------------------------------------------------------------------------
# 数据类
# ---------------------------------------------------------------------------


@dataclass
class ParsedTable:  # noqa: D101
    name: str
    dataframe: pd.DataFrame
    meta: dict


class HeaderDetectionError(RuntimeError):  # noqa: D401
    """LLM 无法识别表头结构时抛出。"""


# ---------------------------------------------------------------------------
# 主类实现
# ---------------------------------------------------------------------------


class ExcelParser:  # noqa: D101
    def __init__(self, root_dir: Path | str = "data_files") -> None:  # noqa: D401, D107
        self.root_dir = Path(root_dir)
        if not self.root_dir.exists():
            raise FileNotFoundError(f"目录不存在: {self.root_dir}")

    # ------------------------------------------------------------
    # 公共 API
    # ------------------------------------------------------------

    def discover_excels(self) -> List[Path]:  # noqa: D401
        """扫描 root_dir，返回所有 .xlsx 文件路径。"""
        return [p for p in self.root_dir.rglob("*.xlsx") if p.is_file()]

    def parse_excel(self, path: Path | str) -> Iterator[ParsedTable]:  # noqa: D401
        """解析单个 Excel 文件，返回 ParsedTable 生成器。"""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)

        wb = openpyxl.load_workbook(str(path), data_only=True, read_only=False)  # type: ignore[arg-type]

        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            df = self._sheet_to_dataframe(ws)
            if df.empty:
                continue

            try:
                title_rows, data_start, desc_rows = self._infer_header_llm(df.head(10))
            except HeaderDetectionError as e:
                logger.warning(
                    "LLM 识别表头失败，将使用启发式解析 Sheet %s/%s: %s", path.name, sheet_name, e
                )
                # --- 简单启发式：第一行作为表头，其余为数据区 -----------------
                non_empty_rows = df.dropna(how="all").index.tolist()
                if not non_empty_rows:
                    continue  # 空 Sheet
                first_row = non_empty_rows[0]
                title_rows = [first_row]
                data_start = first_row + 1
                desc_rows = []
            else:
                # 若 LLM 给出的行号超出实际范围，也回退到启发式
                if data_start >= len(df) or (title_rows and max(title_rows) >= len(df)):
                    logger.debug("LLM 行号超出范围，回退启发式解析 Sheet %s/%s", path.name, sheet_name)
                    non_empty_rows = df.dropna(how="all").index.tolist()
                    if not non_empty_rows:
                        continue
                    first_row = non_empty_rows[0]
                    title_rows = [first_row]
                    data_start = first_row + 1
                    desc_rows = []

            # 新增：提取描述行文本，便于后续存储与向量化
            desc_texts: List[str] = []
            for r in desc_rows:
                if r >= len(df):
                    continue
                row_vals = df.iloc[r]
                cells = [str(c).strip() for c in row_vals if pd.notna(c) and str(c).strip()]
                if cells:
                    desc_texts.append(" ".join(cells))
            description_text = "\n".join(desc_texts)

            # 取消硬编码非空阈值，将行判定完全交由 LLM

            headers = self._build_column_names(df, title_rows)

            body_df = df.iloc[data_start:].reset_index(drop=True)
            body_df.columns = headers
            body_df = body_df.dropna(axis=1, how="all")
            body_df = body_df.replace(r"^\s*$", pd.NA, regex=True).convert_dtypes()

            if body_df.isna().values.all():
                continue

            table_name = self._generate_table_name(path.stem, sheet_name)
            meta: Dict = {
                "description_rows": desc_rows,
                "title_rows": title_rows,
                "data_start_row": data_start,
                "sheet_name": sheet_name,
                "description_text": description_text,  # <-- 新增字段
            }
            yield ParsedTable(name=table_name, dataframe=body_df, meta=meta)

    # ------------------------------------------------------------
    # 内部实现
    # ------------------------------------------------------------

    @staticmethod
    def _sheet_to_dataframe(ws: "Any") -> pd.DataFrame:  # noqa: D401
        """取消所有合并单元格并复制值，然后转为 DataFrame。"""
        # 复制合并区域的值，并取消合并
        for rng in list(ws.merged_cells.ranges):
            tl_value = ws.cell(row=rng.min_row, column=rng.min_col).value
            ws.unmerge_cells(str(rng))
            for row in ws.iter_rows(
                min_row=rng.min_row,
                max_row=rng.max_row,
                min_col=rng.min_col,
                max_col=rng.max_col,
            ):
                for cell in row:
                    cell.value = tl_value

        data = [list(row) for row in ws.iter_rows(values_only=True)]
        df = pd.DataFrame(data)
        # 删除 Sheet 尾部和顶部的全空行
        df = df.dropna(how="all").reset_index(drop=True)
        return df

    # ------------------------------------------------------------
    # LLM 调用，识别表头
    # ------------------------------------------------------------

    def _infer_header_llm(
        self, sample_df: pd.DataFrame
    ) -> Tuple[List[int], int, List[int]]:
        """调用 LLM 识别表头、数据起始行。

        新增：如第一次解析失败，等待 5 秒后重试一次；两次都失败则抛出
        :class:`HeaderDetectionError` 交由上层启发式解析。
        """

        import time  # 本地导入避免额外全局依赖

        def _call_llm(prompt: str):  # noqa: D401
            """调用 LLM，按 header_detection 任务配置选择模型/服务。"""
            cfg = get_task_cfg("header_detection")
            
            provider = cfg.get("provider")
            
            # SiliconFlow 不支持 response_format 参数
            if provider == "siliconflow":
                return ask(
                    prompt=prompt,
                    system=cfg.get("system_prompt")
                    or "你是一名专业的数据表解析专家。请严格遵循用户指令，仅以 JSON 对象形式回答。",
                    provider=provider,
                    model=cfg.get("model"),
                    temperature=float(cfg.get("temperature", 0.1)),
                    max_tokens=int(cfg.get("max_tokens", 4096)),
                    think=bool(cfg.get("think", False)),
                )
            else:
                return ask(
                    prompt=prompt,
                    system=cfg.get("system_prompt")
                    or "你是一名专业的数据表解析专家。请严格遵循用户指令，仅以 JSON 对象形式回答。",
                    provider=provider,
                    model=cfg.get("model"),
                    response_format={"format": "json"},
                    temperature=float(cfg.get("temperature", 0.1)),
                    max_tokens=int(cfg.get("max_tokens", 4096)),
                    think=bool(cfg.get("think", False)),
                )

        # ---------------- 清洗 sample_df 以减少噪声 ----------------
        # 1) 移除行索引（index=False）
        # 2) 截断过长内容，保留前 15 个字符，防止 prompt 过大
        # 3) 替换换行 / 竖线，避免干扰 Markdown 渲染

        def _clean_cell(val: Any) -> str:  # noqa: D401
            if pd.isna(val):
                return ""
            s = str(val).replace("\n", " ").replace("|", "/").strip()
            TRUNC = 15
            return s if len(s) <= TRUNC else s[:TRUNC] + "…"

        clean_df = sample_df.applymap(_clean_cell).copy()

        # （可选）若列数非常多，仅保留前 30 列，避免 prompt 长度爆炸
        if clean_df.shape[1] > 30:
            clean_df = clean_df.iloc[:, :30]


        # 4) 生成 Markdown，但移除 to_markdown 自带的表头与分隔行
        #    因为我们希望 LLM 自己分析，不希望被 pandas 的默认数字表头干扰
        raw_markdown = clean_df.to_markdown(index=False, tablefmt="github") or ""
        markdown_lines = raw_markdown.split("\n")
        
        # 移除表头（第 0 行）和分隔符（第 1 行）
        if len(markdown_lines) > 2:
            markdown_table = "\n".join(markdown_lines[2:])
        else:
            markdown_table = "" # 空表或格式错误

        # Note: 临时移除这句话从prompt中，因为模型会意外输出这个内容。 
        # non_empty = sample_df.notna().sum(axis=1).tolist()
        # density_info = ", ".join(str(c) for c in non_empty)
        # 你可以参考表格中各行非空单元格数量: [{density_info}]，来判断表格的结构。\n\n
        prompt = (
            f"""下面是一个表格的 Markdown 渲染，请分析其结构：\n\n
{markdown_table}\n\n
请完成三件事（务必返回数字行号）：
1. 识别描述行 (description_rows): 对整个表的描述，通常在表头之前。
2. 识别标题行 (title_rows): 列标题，请特别注意识别多行标题的情况。
3. 识别数据起始行 (data_start_row): 正式数据的第一行。

你应该从字段类型入手，通常标题行和实际数据行的字段类型是不同的，此外标题行通常不包含空单元格，也是一个判断的参考依据。

严格按照下方样例 JSON 格式输出，不要添加任何额外文字：
{{\"description_rows\":[n], \"title_rows\":[m,p], \"data_start_row\": o}}

其中n,m,p,o必须为数字，n,m,p,o的取值范围为0到表格行数-1。
若某项不存在，返回空数组 []；若无法判断表头，title_rows 必须返回空数组。"""

        )

        # -------- 重试机制 --------
        MAX_RETRIES = 1  # 首次失败后再重试 1 次

        last_exc: Exception | None = None
        for attempt in range(MAX_RETRIES + 1):
            try:
                resp = _call_llm(prompt)
                logger.debug("LLM raw response: %s", resp)

                # 先移除 <think> 包裹，避免误干扰 JSON 解析
                import re

                _think_pattern = re.compile(r"<think>.*?</think>", re.S)
                cleaned_resp = _think_pattern.sub("", resp)

                parsed = json.loads(_extract_json_block(cleaned_resp))

                def _to_int_list(seq: Any) -> List[int]:
                    if not isinstance(seq, list):
                        return []
                    out = []
                    for x in seq:
                        try:
                            out.append(int(x))
                        except (ValueError, TypeError):
                            continue
                    return out

                # ---------------- 这里假设 parsed 必须是 dict ----------------
                if not isinstance(parsed, dict):
                    raise ValueError("LLM response is not a JSON object")

                title_rows = _to_int_list(parsed.get("title_rows", []))
                description_rows = _to_int_list(parsed.get("description_rows", []))

                if not title_rows:
                    raise ValueError("LLM failed to return 'title_rows'")

                data_start = int(parsed.get("data_start_row", max(title_rows) + 1))
                if description_rows:
                    logger.debug("识别到描述行: %s", description_rows)
                return title_rows, data_start, description_rows

            except (
                LLMClientError,
                ValueError,
                json.JSONDecodeError,
                TypeError,
                AttributeError,
            ) as exc:
                last_exc = exc
                if attempt < MAX_RETRIES:
                    logger.warning(
                        "LLM header detection failed (attempt %s/%s): %s，5s 后重试…",
                        attempt + 1,
                        MAX_RETRIES + 1,
                        exc,
                    )
                    time.sleep(5)
                    continue
                break  # 最后一次也失败

        assert last_exc is not None  # 为 mypy
        raise HeaderDetectionError(f"LLM failed to detect header after retry: {last_exc}") from last_exc

    # ------------------------------------------------------------
    # 多级表头拼接与列名清洗
    # ------------------------------------------------------------

    def _build_column_names(self, df: pd.DataFrame, title_rows: List[int]) -> List[str]:  # noqa: D401
        num_cols = df.shape[1]
        names: List[str] = []
        for c in range(num_cols):
            titles: List[str] = []
            last = None
            for r in title_rows:
                val = df.iat[r, c]
                s = str(val).strip() if val is not None and str(val).strip() else ""
                if not s:
                    continue
                if last is None or s != last:  # 去除同级重复
                    titles.append(s)
                    last = s
            header = " / ".join(titles) if titles else f"col_{c}"
            names.append(header)
        names = self._sanitize_columns(names)
        return names

    # ------------------------------------------------------------
    # 工具方法（沿用旧实现）
    # ------------------------------------------------------------

    @staticmethod
    def _sanitize_columns(columns: List[str]) -> List[str]:  # noqa: D401
        import re

        pattern = re.compile(r"[^0-9A-Za-z_\u4e00-\u9fff]+")  # 允许中文/数字/字母/下划线

        clean_cols: List[str] = []
        counter: dict[str, int] = {}
        for i, col in enumerate(columns):
            raw = str(col).strip() if col and str(col).strip() else ""
            col_name = pattern.sub("_", raw).strip("_") or f"col_{i}"
            if col_name in counter:
                counter[col_name] += 1
                col_name = f"{col_name}_{counter[col_name]}"
            else:
                counter[col_name] = 0
            clean_cols.append(col_name)
        return clean_cols

    @staticmethod
    def _generate_table_name(file_stem: str, sheet_name: str) -> str:  # noqa: D401
        import re
        import hashlib

        def sanitize(s: str) -> str:
            # 允许中文
            s = re.sub(r"[^0-9A-Za-z_\u4e00-\u9fff]+", "_", s)
            return s.strip("_")

        base = f"{sanitize(file_stem)}_{sanitize(sheet_name)}".strip("_")
        if not base:
            base = hashlib.md5(sheet_name.encode()).hexdigest()[:8]
        # SQLite table name length limit 64
        if len(base) > 60:
            suffix = hashlib.md5(base.encode()).hexdigest()[:4]
            base = base[:55] + "_" + suffix
        return base


# 顶级函数接口，方便直接调用

def parse_excel(path: Path | str) -> List[ParsedTable]:  # noqa: D401
    """函数式封装，兼容 docs 中的 API 设计。"""
    return list(ExcelParser(Path(path).parent).parse_excel(path)) 