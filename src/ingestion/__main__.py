from __future__ import annotations
from .runner import main

"""Package entrypoint for `python -m src.ingestion`.

It now launches the rich CLI defined in :pymod:`src.ingestion.runner`, which
provides progress bars, logging, Ollama checks, etc.  The previous lightweight
wrapper (:pymod:`src.ingestion.cli`) has been removed during the cleanup.
"""

if __name__ == "__main__":  # pragma: no cover
    main() 