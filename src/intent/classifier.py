from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List
import logging

import yaml  # type: ignore

"""Intent classifier driven by YAML rules, with optional LLM fallback."""


logger = logging.getLogger(__name__)

RULE_PATH = Path("config/intent_rules.yaml")


@dataclass
class IntentResult:  # noqa: D101
    intent: str
    strategy: str
    params: Dict[str, Any]
    rule_index: int | None = None
    level_probs: Dict[str, float] | None = None  # 新增字段：file/sheet/row 概率分布


class IntentClassifier:  # noqa: D101
    def __init__(self, rules_path: Path | str = RULE_PATH, *, task_cfg: Dict[str, Any] | None = None) -> None:  # noqa: D401
        """IntentClassifier constructor.

        Parameters
        ----------
        rules_path : Path | str, optional
            YAML 规则文件路径。
        task_cfg : dict | None, optional
            来自流水线步骤的 intent_classifier 配置，包含 provider / model / prompt 等，
            若为空则回退到全局 app_settings.yaml 的 tasks.intent_classification。
        """
        self.task_cfg: Dict[str, Any] = task_cfg or {}
        self.rules_path = Path(rules_path)
        self._load_rules()

    def _load_rules(self) -> None:  # noqa: D401
        if not self.rules_path.exists():
            logger.warning("Intent rule file not found: %s", self.rules_path)
            self.rules: List[Dict[str, Any]] = []
            return
        with open(self.rules_path, "r", encoding="utf-8") as f:
            self.rules = yaml.safe_load(f) or []
        logger.info("Loaded %d intent rules", len(self.rules))

    def reload(self) -> None:  # noqa: D401
        self._load_rules()

    # --- main API -----------------------------------------------------
    def classify(self, question: str) -> IntentResult:  # noqa: D401
        """一次大模型调用同时获取层级概率，并据此推断 intent。"""

        # ------------------------------------------------------------------
        # 1) 首先尝试调用 LLM 返回 {file, sheet, row} 概率分布。
        #    根据最大概率对应的层级推断 intent，无需第二次 LLM 调用。
        # ------------------------------------------------------------------
        probs = self._intent_classification(question)

        if probs:  # LLM 返回成功
            # -------------------- 处理不相关 ----------------------------
            if "unrelated" in probs:
                return IntentResult(
                    intent="unrelated",
                    strategy="none",
                    params={},
                    level_probs=probs,
                )

            # 推断意图：概率最大的键即 intent，例如 row / sheet / file
            intent_name = max(probs, key=lambda k: probs[k])

            # 根据 YAML 规则查找对应策略/参数
            rule = next((r for r in self.rules if r.get("intent") == intent_name), None)

            if rule is not None:
                return IntentResult(
                    intent=intent_name,
                    strategy=rule.get("strategy", "vector_sheet"),
                    params=rule.get("params", {}),
                    rule_index=self.rules.index(rule),
                    level_probs=probs,
                )

        # ------------------------------------------------------------------
        # 2) 若 LLM 调用失败（或返回无法解析的 JSON），回退关键词规则。
        #    此时 _level_probs(question) 可能会再次尝试 LLM，但若仍失败则用启发式。
        # ------------------------------------------------------------------
        for idx, rule in enumerate(self.rules):
            if any(kw in question for kw in rule.get("keywords", [])):
                result = IntentResult(
                    intent=rule.get("intent", "unknown"),
                    strategy=rule.get("strategy", "vector_sheet"),
                    params=rule.get("params", {}),
                    rule_index=idx,
                )
                result.level_probs = self._level_probs(question)
                return result

        # ------------------------------------------------------------------
        # 3) 最终兜底：unknown
        # ------------------------------------------------------------------
        default_result = IntentResult(
            intent="unknown",
            strategy="vector_sheet",
            params={"level": "sheet", "top_k": 3},
        )
        default_result.level_probs = self._level_probs(question)
        return default_result

    # ------------------------------------------------------------------
    # LLM Helper
    # ------------------------------------------------------------------

    def _level_probs(self, question: str) -> Dict[str, float]:  # noqa: D401
        """返回 file/sheet/row 三个层次的概率分布，优先调用 LLM，否则回退启发式。"""
        probs = self._intent_classification(question)
        if probs:
            return probs
        # ---------------- 规则回退 ----------------
        # 非严格统计，仅根据关键词粗略估计
        lower_q = question.lower()
        if any(k in lower_q for k in ["文件", "workbook", "file"]):
            return {"file": 0.6, "sheet": 0.3, "row": 0.1}
        if any(k in lower_q for k in ["行", "record", "row"]):
            return {"file": 0.1, "sheet": 0.3, "row": 0.6}
        # 默认认为 Sheet 级概率最高
        return {"file": 0.2, "sheet": 0.5, "row": 0.3}

    # ------------------------------------------------------------------
    # New helper: LLM to get probability distribution
    # ------------------------------------------------------------------
    def _intent_classification(self, question: str) -> "Dict[str, float] | None":  # noqa: D401
        """调用 LLM 返回层次概率分布，若失败返回 None。"""
        # 从全局配置加载自定义 prompt（若存在）
        try:
            from src.utils.config import get_task_cfg  # pylint: disable=import-error

            level_cfg = self.task_cfg if self.task_cfg else get_task_cfg("intent_classification")
            prompt_cfg = level_cfg.get("prompt")
            provider_cfg = {
                "provider": level_cfg.get("provider"),
                "model": level_cfg.get("model"),
            }

            # 读取 think 配置
            think_flag = level_cfg.get("think")
        except Exception:  # noqa: BLE001
            prompt_cfg = None
            provider_cfg = {"provider": None, "model": None}
            think_flag = None

        base_prompt = prompt_cfg or (
            "你是一个查询意图分类器。我们的数据按照 file、sheet、row 三个层次存储。"
            "请分析用户问题，判断答案最有可能来自哪个层次，并返回一个 JSON 对象，"
            "包含 file、sheet、row 三个键，各自对应概率 (0-1 之间，且和为 1)。"
            "仅回复 JSON，无需解释。"
            "判定准则补充："
            "• 如果问题重点是『某列/字段的默认值、取值范围、数据类型』，答案来自【行级(row)】。"
            "• 如果问题关注整张表的用途、结构、更新频度等，答案来自【表级(sheet)】。"
            "• 如果问题涉及整个文件/工作簿的统计或概况，答案来自【文件级(file)】。"
        )

        # ------------------------------------------------------------------
        # 追加 intent_rules.yaml 中的示例，帮助大模型更精确识别层级。
        # ------------------------------------------------------------------
        examples_parts: List[str] = []
        for rule in self.rules:
            exs = rule.get("examples")
            if not exs:
                continue
            intent_name = rule.get("intent", "unknown")
            joined = "\n".join(f"- {e}" for e in exs)
            examples_parts.append(f"{intent_name} 示例:\n{joined}")
        examples_block = "\n\n".join(examples_parts)

        system_prompt = base_prompt
        if examples_block:
            system_prompt += f"\n\n{examples_block}"

        try:
            from src.parser.llm.client import ask, LLMClientError  # type: ignore
            import json
            rsp = ask(
                f"用户问题：{question}",
                system=system_prompt,
                temperature=0,
                response_format={"type": "json_object"},
                provider=provider_cfg.get("provider"),
                model=provider_cfg.get("model"),
                think=think_flag,
            )
            # --------------------------------------------------------------
            # 若大模型判定问题与监管领域无关，会直接返回 “不相关”。
            # 此时立即返回特殊概率分布，供上层逻辑识别并提前终止流程。
            # --------------------------------------------------------------
            if rsp.strip().startswith("不相关"):
                return {"unrelated": 1.0}
            # 尝试直接解析
            try:
                data = json.loads(rsp)
            except json.JSONDecodeError:
                import re

                match = re.search(r"\{.*?\}", rsp, re.S)
                if not match:
                    return None
                data = json.loads(match.group(0))

            # 简单校验
            if not isinstance(data, dict):
                return None
            probs: Dict[str, float] = {}
            for key in ("file", "sheet", "row"):
                if key not in data:
                    return None
                try:
                    probs[key] = float(data[key])
                except (TypeError, ValueError):
                    return None
            total = sum(probs.values())
            if total <= 0:  # noqa: WPS507
                return None
            # 归一化
            probs = {k: v / total for k, v in probs.items()}
            return probs
        except (ImportError, LLMClientError, Exception):  # noqa: BLE001
            return None 