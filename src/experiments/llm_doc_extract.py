"""Experiment T4: extract description / rule text above header and have LLM classify paragraphs.
"""
from __future__ import annotations
import arg<PERSON><PERSON>, json, re
from pathlib import Path
from typing import List
import openpyxl
from openpyxl.worksheet.worksheet import Worksheet
from src.parser.llm.client import ask
from src.experiments.llm_probe import task_header_detect, load_snippet

def get_pre_header_text(ws: Worksheet, header_row: int, max_rows: int = 10) -> str:
    lines: List[str] = []
    for i in range(1, min(header_row, max_rows+1)):
        row_vals = [str(c.value).strip() if c.value is not None else "" for c in ws[i]]
        joined = " ".join(filter(None, row_vals))
        if joined:
            lines.append(joined)
    return "\n".join(lines)

def sanitize_json(text: str) -> str:
    """Extract first JSON array or object from LLM output."""
    m = re.search(r"\[[\s\S]*?\]", text)
    if not m:
        m = re.search(r"\{[\s\S]*?\}", text)
    if not m:
        raise ValueError("No JSON found in LLM response")
    snippet = m.group(0)
    # rudimentary fix for trailing commas inside arrays/objects
    snippet = re.sub(r",\s*([}\]])", r"\1", snippet)
    return snippet

def classify_sections(sheet: str, paragraphs: List[str]):
    para_json = json.dumps(paragraphs, ensure_ascii=False)
    prompt = f"""
你是一位金融监管数据专家，擅长阅读 EAST5.0 报送说明。
请将下列段落按内容类型分类，类型集合固定为：
  - intro: 表整体介绍/返回目录
  - note: 备注/说明
  - rule: 校验/非空/格式/表间规则
返回 JSON 数组，每项 {{"type":str, "text":str}}，保持原顺序。
段落列表: {para_json}
"""
    raw = ask(prompt, temperature=0.1, response_format={"type": "json_object"})
    return json.loads(sanitize_json(raw))

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--file", type=Path, required=True)
    ap.add_argument("--sheet", type=str, required=True)
    args = ap.parse_args()

    wb = openpyxl.load_workbook(args.file, read_only=True, data_only=True)
    ws = wb[args.sheet]

    snippet = load_snippet(ws)
    meta = task_header_detect(args.sheet, snippet)
    header_row = meta["header_row"]

    pre_text = get_pre_header_text(ws, header_row)
    if not pre_text:
        print("No pre-header text found")
        return
    paragraphs = [p.strip() for p in pre_text.split("\n") if p.strip()]
    result = classify_sections(args.sheet, paragraphs)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main() 