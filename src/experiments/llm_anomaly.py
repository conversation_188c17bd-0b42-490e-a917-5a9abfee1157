"""Experiment T5: Let LLM detect anomalies in sample data rows.

Approach: load header + first 30 data rows, inject synthetic anomalies (empty mandatory, invalid date), ask LLM to report issues.
"""
from __future__ import annotations
import argparse, json, random, re, logging
logging.basicConfig(level=logging.INFO)
from pathlib import Path
from typing import List, Dict
import openpyxl
from openpyxl.worksheet.worksheet import Worksheet
from src.parser.llm.client import ask
from src.experiments.llm_probe import task_header_detect, load_snippet

MAX_ROWS = 30


def read_data_rows(ws: Worksheet, header_row: int, n: int = MAX_ROWS) -> List[List[str]]:
    rows: List[List[str]] = []
    for idx, row in enumerate(ws.iter_rows(min_row=header_row+1, values_only=True), start=1):
        if idx > n:
            break
        rows.append([str(c) if c is not None else "" for c in row])
    return rows


def inject_anomalies(rows: List[List[str]], header: List[str]) -> List[List[str]]:
    if not rows:
        return rows
    rows_mod = [r.copy() for r in rows]
    # choose a random row and column to blank mandatory value
    blank_idx = random.randint(0, len(rows_mod)-1)
    col_idx = 0  # assume first column mandatory
    rows_mod[blank_idx][col_idx] = ""
    # invalid date on another random row
    if "成立日期" in header:
        date_col = header.index("成立日期")
        rows_mod[0][date_col] = "20251301"  # invalid
    return rows_mod


def build_csv(header: List[str], rows: List[List[str]]) -> str:
    lines = ["|".join(header)]
    for r in rows:
        lines.append("|".join(r))
    return "\n".join(lines)


def prompt_anomaly(csv_text: str) -> str:
    prompt = (
        "你是一位数据质检专家，请使用中文回复。以下内容为监管报送表的部分数据记录，字段以竖线 '|' 分隔，第一行为表头。\n\n"
        "请找出以下异常类型：\n"
        "1. 必填字段为空；\n2. 日期格式非法（应为 YYYYMMDD）；\n3. 金额为负等明显不合理数值。\n\n"
        "只需输出 **JSON 数组**，不要输出任何解释性文字。数组元素格式示例：\n"
        "[{{\"row\":1, \"col\":\"字段名\", \"issue\":\"问题\", \"suggestion\":\"修正建议\"}}]\n\n"
        "数据片段：\n" + csv_text
    )
    raw = ask(prompt, temperature=0.1, response_format={"type": "json_object"})
    return raw


def sanitize_json(text: str) -> str:
    m = re.search(r"\[[\s\S]*?\]", text)
    if not m:
        obj = re.search(r"\{[\s\S]*?\}", text)
        if obj:
            return obj.group(0)
        logging.error("LLM raw output without JSON array: %s", text)
        raise ValueError("no json array")
    snippet = re.sub(r",\s*]", "]", m.group(0))
    return snippet


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--file", type=Path, required=True)
    ap.add_argument("--sheet", type=str, required=True)
    args = ap.parse_args()

    wb = openpyxl.load_workbook(args.file, read_only=True, data_only=True)
    ws = wb[args.sheet]

    snippet = load_snippet(ws)
    meta = task_header_detect(args.sheet, snippet)
    if isinstance(meta, dict) and "header_row" in meta:
        header_row = meta["header_row"]
    else:
        logging.warning("LLM header detect failed or missing key, fallback row=1; meta=%s", meta)
        header_row = 1

    header_cells = [str(c.value) if c.value else "" for c in ws[header_row]]
    rows = read_data_rows(ws, header_row)
    rows_mod = inject_anomalies(rows, header_cells)

    csv_text = build_csv(header_cells, rows_mod)
    raw = prompt_anomaly(csv_text)
    print(sanitize_json(raw))

if __name__ == "__main__":
    main() 