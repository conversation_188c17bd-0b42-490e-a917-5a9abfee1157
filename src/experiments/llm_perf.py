"""Experiment: benchmark token usage and latency under various prompt truncation and batch sizes.

Run:
    python -m src.experiments.llm_perf --output output/perf_metrics.csv

Environment variables:
    COST_PER_1K_INPUT – unit cost per 1K input tokens (USD), default 0.0001
    COST_PER_1K_OUTPUT – unit cost per 1K output tokens (USD), default 0.0003
    HEADER_ROWS – not used here but reused by other scripts.
"""
from __future__ import annotations
import argparse, csv, json, logging, os, time
from pathlib import Path
from typing import List, Dict

import random

try:
    import tiktoken  # type: ignore
except ImportError:  # graceful degrade
    tiktoken = None  

from src.parser.llm.client import ask

logging.basicConfig(level=logging.INFO)

# Configurable parameters
TOKEN_LENGTHS = [256, 512, 1024]
BATCH_SIZES = [1, 4, 8]
REPEATS = 2  # number of repeats per combination to average

COST_IN = float(os.getenv("COST_PER_1K_INPUT", "0.0001"))
COST_OUT = float(os.getenv("COST_PER_1K_OUTPUT", "0.0003"))


def build_prompt(token_len: int) -> str:
    """Construct a dummy prompt approximately of token_len tokens."""
    # Use lorem ipsum sample to approximate tokens (4 chars per token typical)
    word = "数据"
    repeat = token_len * 4 // len(word)
    return (word * repeat)[: token_len * 4]


def count_tokens(text: str) -> int:
    if tiktoken is None:
        return len(text) // 4  # rough estimate
    enc = tiktoken.get_encoding("cl100k_base")
    return len(enc.encode(text))


def benchmark(token_len: int, batch: int) -> Dict:
    prompts = [build_prompt(token_len) for _ in range(batch)]
    token_in = sum(count_tokens(p) for p in prompts)

    start = time.perf_counter()
    outputs: List[str] = []
    for p in prompts:
        outputs.append(ask(p, temperature=0.0, max_tokens=64))
    duration = time.perf_counter() - start

    token_out = sum(count_tokens(o) for o in outputs)
    cost = (token_in / 1000) * COST_IN + (token_out / 1000) * COST_OUT

    return {
        "token_len": token_len,
        "batch": batch,
        "token_in": token_in,
        "token_out": token_out,
        "duration_s": round(duration, 3),
        "cost_est_usd": round(cost, 4),
    }


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--output", type=Path, required=False, default=Path("output/perf_metrics.csv"))
    args = ap.parse_args()

    results: List[Dict] = []
    for t in TOKEN_LENGTHS:
        for b in BATCH_SIZES:
            for _ in range(REPEATS):
                logging.info("Benchmarking tokens=%s batch=%s", t, b)
                res = benchmark(t, b)
                results.append(res)

    # Ensure output dir
    args.output.parent.mkdir(parents=True, exist_ok=True)

    # Write CSV
    with args.output.open("w", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=list(results[0].keys()))
        writer.writeheader()
        writer.writerows(results)

    # Also print summary in pretty JSON
    print(json.dumps(results, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main() 