"""Experiment T6: merge multi-line headers into single unified field list using LLM."""
from __future__ import annotations
import argparse, json, logging, re
from pathlib import Path
from typing import List
import openpyxl
from openpyxl.worksheet.worksheet import Worksheet
from src.parser.llm.client import ask
import os

logging.basicConfig(level=logging.INFO)

MAX_HEADER_ROWS = int(os.getenv("HEADER_ROWS", "10"))


def extract_header_block(ws: Worksheet) -> str:
    """Return first MAX_HEADER_ROWS rows as pipe separated lines."""
    lines: List[str] = []
    for idx, row in enumerate(ws.iter_rows(min_row=1, max_row=MAX_HEADER_ROWS, values_only=True), start=1):
        cells = [str(c) if c else "" for c in row]
        lines.append(f"<ROW {idx}> | " + " | ".join(cells))
    return "\n".join(lines)


def sanitize_json(text: str) -> str:
    arr = re.search(r"\[[\s\S]*?\]", text)
    if arr:
        return arr.group(0)
    obj = re.search(r"\{[\s\S]*?\}", text)
    if obj:
        return obj.group(0)
    logging.error("LLM output not JSON: %s", text)
    raise ValueError("no json")


def prompt_merge(sheet: str, snippet: str) -> List[str]:
    prompt = (
        "你是一位数据治理专家，请使用中文回复。以下是工作表多行字段标题的片段，每行以 '|' 分隔。\n"
        "请根据合并单元格及上下文，将这些多行标题合并为单行字段列表，保持原顺序，直接输出 JSON 数组 (无需解释)。\n"
        "如果信息不足也要尽量合并，不要返回错误信息或解释性文本。\n\n"
        f"片段:\n{snippet}"
    )
    raw = ask(prompt, temperature=0.1, response_format={"type": "json_object"})
    clean = sanitize_json(raw)
    try:
        data = json.loads(clean)
    except Exception as e:
        logging.error("JSON parse error: %s", e)
        raise

    # 多种可能结构容错处理
    if isinstance(data, list):
        return data

    if isinstance(data, dict):
        # 常见 key 优先
        for k in ("fields", "headers", "result", "data"):
            if k in data and isinstance(data[k], list):
                return data[k]
        # 退而求其次：第一个 list value
        for v in data.values():
            if isinstance(v, list):
                return v

    # 若包含 error 信息，降级返回空列表
    if isinstance(data, dict) and "error" in data:
        logging.warning("LLM 返回 error 字段，降级为空列表: %s", data["error"])
        return []

    raise ValueError("unexpected JSON structure")


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--file", type=Path, required=True)
    ap.add_argument("--sheet", type=str, required=True)
    args = ap.parse_args()

    wb = openpyxl.load_workbook(args.file, read_only=True, data_only=True)

    sheet_name = args.sheet
    # 若传入数字索引，转换为工作表名称
    if sheet_name.isdigit():
        idx = int(sheet_name)
        try:
            sheet_name = wb.sheetnames[idx]
        except IndexError:
            logging.error("工作表索引 %s 超出范围，当前共有 %d 个工作表。", idx, len(wb.sheetnames))
            print("可用工作表：", wb.sheetnames)
            return

    if sheet_name not in wb.sheetnames:
        logging.error("指定工作表 '%s' 不存在。", sheet_name)
        print("可用工作表：", wb.sheetnames)
        return

    ws = wb[sheet_name]

    snippet = extract_header_block(ws)
    fields = prompt_merge(args.sheet, snippet)
    print(json.dumps(fields, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main() 