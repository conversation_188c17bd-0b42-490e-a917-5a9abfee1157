"""Experiment T3: Use LLM to map Chinese field names to EAST 5.0 standard dictionary.

Steps:
1. Detect header row via previous LLM header task or simple heuristics.
2. Provide field list and a small standard dict to LLM.
3. Ask LLM to return mapping JSON list.
"""
from __future__ import annotations

import argparse
import json
from pathlib import Path
from typing import List

import openpyxl
from openpyxl.worksheet.worksheet import Worksheet

from src.parser.llm.client import ask
from src.experiments.llm_probe import load_snippet, task_header_detect  # reuse

STANDARD_DICT = [
    {"code": "ORG_CODE", "name": "银行机构代码"},
    {"code": "ORG_NO", "name": "内部机构号"},
    {"code": "LICENSE_NO", "name": "金融许可证号"},
    {"code": "ORG_NAME", "name": "银行机构名称"},
    {"code": "ORG_TYPE", "name": "机构类别"},
    {"code": "ADMIN_CODE", "name": "行政区划代码"},
    {"code": "STATUS", "name": "营业状态"},
    {"code": "SETUP_DATE", "name": "成立日期"},
    {"code": "ORG_ADDR", "name": "机构地址"},
]


def prompt_mapping(fields: List[str]) -> str:
    dict_json = json.dumps(STANDARD_DICT, ensure_ascii=False)
    fields_json = json.dumps(fields, ensure_ascii=False)
    prompt = f"""
你是一位数据标准专家。下面给出 EAST5.0 标准字段字典 (code, name)，以及待映射的中文字段列表。
请为每个字段找到最匹配的标准字段 code，返回 JSON 数组，每项包含 src, target, score(0-1)。
若无法匹配，target 置 null，score 置 0。

标准字段字典: {dict_json}
待映射字段: {fields_json}

输出范例: [{{"src": "银行机构代码", "target": "ORG_CODE", "score": 0.95}}, ...]
"""
    raw = ask(prompt, temperature=0.1, response_format={"type": "json_object"})
    return raw


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--file", type=Path, required=True)
    ap.add_argument("--sheet", type=str, required=True)
    args = ap.parse_args()

    wb = openpyxl.load_workbook(args.file, read_only=True, data_only=True)
    ws: Worksheet = wb[args.sheet]

    snippet = load_snippet(ws)
    meta = task_header_detect(args.sheet, snippet)
    header_row = meta["header_row"]

    # read header row values from workbook for reliability
    header_cells = [cell.value for cell in ws[header_row]]
    fields = [str(c).strip() for c in header_cells if c]

    raw = prompt_mapping(fields)
    print(raw)


if __name__ == "__main__":
    main() 