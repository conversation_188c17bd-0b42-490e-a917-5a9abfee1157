"""Quick experimental script to test SiliconFlow LLM with sheet classification & header detection tasks.

Usage:
    python -m src.experiments.llm_probe --file data_files/CBRC_EAST5.0_业务Mapping_V2.2.4.xlsx --sheet 机构信息表

Env:
    SILICONFLOW_API_KEY must be defined.
"""
from __future__ import annotations

import argparse
from pathlib import Path
from typing import List
import json, re, logging
logging.basicConfig(level=logging.INFO)

import openpyxl
from openpyxl.worksheet.worksheet import Worksheet

from src.parser.llm.client import ask

HEAD_ROWS = 40
TAIL_ROWS = 5


def load_snippet(ws: Worksheet, head: int = HEAD_ROWS, tail: int = TAIL_ROWS) -> str:
    """Return a string snippet with first `head` rows and last `tail` rows as pipe-separated lines."""
    lines: List[str] = []
    max_row = ws.max_row or 0

    def row_line(idx: int, row) -> str:
        cells = [str(cell) if cell is not None else "" for cell in row]
        return f"<ROW {idx}> | " + " | ".join(cells)

    # head
    for i, row in enumerate(ws.iter_rows(min_row=1, max_row=min(head, max_row), values_only=True), start=1):
        lines.append(row_line(i, row))

    # tail
    if max_row > head + tail:
        start_tail = max_row - tail + 1
        for i, row in enumerate(ws.iter_rows(min_row=start_tail, max_row=max_row, values_only=True), start=start_tail):
            lines.append(row_line(i, row))

    return "\n".join(lines)


def sanitize_json(text: str) -> str:
    """Extract first JSON object from text and clean common issues."""
    candidates = re.findall(r"\{[\s\S]*?\}", text)
    if not candidates:
        logging.error("LLM raw output without JSON: %s", text)
        raise ValueError("No JSON object found in LLM output")
    snippet = None
    for c in candidates[::-1]:  # prefer last occurrence
        try:
            json.loads(c)
            snippet = c
            break
        except Exception:
            continue
    if snippet is None:
        logging.error("No valid JSON found, candidates: %s", candidates)
        raise ValueError("No valid JSON object")
    snippet = re.sub(r",\s*}\s*$", "}", snippet)
    return snippet


def task_sheet_type(sheet_name: str, snippet: str):
    prompt = (
        "你是一位资深数据治理专家，擅长解析监管报送 Excel 表。\n"
        "请根据以下工作表名称与表格片段，判断该工作表类型：\n"
        "  - DATA: 业务数据表\n  - CODEMAP: 码表/枚举\n  - TEXT: 纯文本说明\n  - SPECIAL: 目录/校验/其他\n\n"
        f"工作表名称: {sheet_name}\n表格片段:\n{snippet}\n\n"
        "严格以 JSON 单行返回: {\"sheet_type\": str, \"confidence\": float}"
    )
    raw = ask(prompt, temperature=0.1, response_format={"type": "json_object"})
    return json.loads(sanitize_json(raw))


def task_header_detect(sheet_name: str, snippet: str):
    prompt = (
        "你是一位资深数据治理专家，擅长解析监管报送 Excel 表结构。\n"
        f"以下是工作表 \"{sheet_name}\" 的部分行(竖线分隔):\n{snippet}\n\n"
        "请找出字段标题所在的行号(从1开始)以及字段列表，并给出置信度(0-1)。\n"
        "严格以 JSON 返回: {\"header_row\": int, \"fields\": [str], \"confidence\": float}"
    )
    raw = ask(prompt, temperature=0.1, response_format={"type": "json_object"})
    return json.loads(sanitize_json(raw))


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--file", type=Path, required=True)
    ap.add_argument("--sheet", type=str, help="Sheet name to test; default first sheet")
    args = ap.parse_args()

    wb = openpyxl.load_workbook(args.file, read_only=True, data_only=True)
    sheet_name = args.sheet or wb.sheetnames[0]
    ws = wb[sheet_name]

    snippet = load_snippet(ws)

    print("=== Task: Sheet Type Classification ===")
    try:
        result1 = task_sheet_type(sheet_name, snippet)
        print(json.dumps(result1, ensure_ascii=False, indent=2))
    except Exception as e:
        print("[Error]", e)

    print("\n=== Task: Header Detection ===")
    try:
        result2 = task_header_detect(sheet_name, snippet)
        print(json.dumps(result2, ensure_ascii=False, indent=2))
    except Exception as e:
        print("[Error]", e)


if __name__ == "__main__":
    main() 