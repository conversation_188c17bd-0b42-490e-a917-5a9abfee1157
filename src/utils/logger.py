from __future__ import annotations

"""统一日志工具。"""

import logging
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path


# NOTE: ``level`` 现在允许传入 ``None`` 或字符串 ("DEBUG" 等)。
# 若为 ``None``，将尝试从 config/app_settings.yaml 读取 ``logging.level``，
# 找不到则回退到 ``INFO``。


def setup_logging(
    log_file: str = "logs/qa_pipeline.log",
    level: int | str | None = None,
    *,
    console: bool = True,
) -> None:  # noqa: D401

    from .config import load_config  # 延迟导入以避免循环依赖

    # ------------------------------------------------------------
    # 1) 解析日志级别
    # ------------------------------------------------------------
    if level is None:
        cfg = load_config()
        level_cfg = cfg.get("logging", {}).get("level")  # type: ignore[arg-type]
        if isinstance(level_cfg, str):
            level = getattr(logging, level_cfg.upper(), logging.INFO)
        else:
            level = logging.INFO

    # 若仍然是字符串，需要转成 logging 常量
    if isinstance(level, str):
        level = getattr(logging, level.upper(), logging.INFO)

    Path(log_file).parent.mkdir(parents=True, exist_ok=True)

    fmt = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    handler = RotatingFileHandler(
        log_file, 
        maxBytes=5 * 1024 * 1024, 
        backupCount=3, 
        encoding="utf-8"
    )
    # 设置为不缓冲日志，确保立即写入文件
    handler.setFormatter(logging.Formatter(fmt))
    
    # 设置立即刷新日志文件
    handler.setLevel(level)  # type: ignore[arg-type]

    root = logging.getLogger()
    root.setLevel(level)  # type: ignore[arg-type]

    # 移除已存在的 StreamHandler，避免 DEBUG 日志打印到控制台
    for h in list(root.handlers):
        # 仅移除不是 RotatingFileHandler 的 StreamHandler
        if isinstance(h, logging.StreamHandler) and not isinstance(h, RotatingFileHandler):
            root.removeHandler(h)

    # 为控制台重新添加 StreamHandler，限制级别为 INFO 及以上
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        # 控制台输出始终使用与文件相同的级别，避免 DEBUG 日志缺失；
        # 若希望固定为 INFO，可改为 logging.INFO
        console_handler.setLevel(level)  # type: ignore[arg-type]
        console_handler.setFormatter(logging.Formatter(fmt))
        root.addHandler(console_handler)
    
    # Suppress extremely verbose logs from markdown_it rules_block parser
    logging.getLogger("markdown_it.rules_block").setLevel(logging.WARNING)
    
    # 避免重复添加 handler
    if not any(isinstance(h, RotatingFileHandler) and h.baseFilename == Path(log_file) for h in root.handlers):
        root.addHandler(handler)
        
    # 配置Python的stdout和stderr不缓冲
    sys.stdout.reconfigure(line_buffering=True)  # type: ignore
    sys.stderr.reconfigure(line_buffering=True)  # type: ignore 