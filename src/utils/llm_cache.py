import os
import json
import sqlite3
import hashlib
import threading
from typing import Dict, Any, Optional

"""LLM 请求响应缓存（持久化）。

- 以 prompt、system、model 等参数序列化后做 MD5 哈希作为主键。
- 使用 SQLite 存储到 output/llm_cache.db，支持并发访问简单锁。
- 通过环境变量 ``LLM_CACHE_DISABLE`` 控制是否启用缓存。
"""

__all__ = ["get_cache", "LLMCache"]


class LLMCache:  # noqa: D101
    def __init__(self, db_path: str = "output/llm_cache.db") -> None:  # noqa: D401
        self.db_path = db_path
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        # ``check_same_thread=False`` 允许跨线程访问；我们自己做线程锁。
        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        self._create_table()
        self.lock = threading.Lock()

    # ------------------------------------------------------------------
    def _create_table(self) -> None:  # noqa: D401
        with self.conn:
            self.conn.execute(
                """
                CREATE TABLE IF NOT EXISTS cache (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
                """
            )

    # ------------------------------------------------------------------
    @staticmethod
    def _hash_params(params: Dict[str, Any]) -> str:  # noqa: D401
        """对参数做 stable JSON dump，再 MD5，确保相同请求得到同一 key。"""
        dumped = json.dumps(params, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(dumped.encode("utf-8")).hexdigest()

    # ------------------------------------------------------------------
    def get(self, params: Dict[str, Any]) -> Optional[str]:  # noqa: D401
        key = self._hash_params(params)
        with self.lock:
            cur = self.conn.execute("SELECT value FROM cache WHERE key = ?", (key,))
            row = cur.fetchone()
        return row[0] if row else None

    # ------------------------------------------------------------------
    def set(self, params: Dict[str, Any], value: str) -> None:  # noqa: D401
        key = self._hash_params(params)
        with self.lock, self.conn:
            self.conn.execute(
                "INSERT OR REPLACE INTO cache(key, value) VALUES (?, ?)", (key, value)
            )


# ----------------------------------------------------------------------
# 单例访问：避免创建多个连接
# ----------------------------------------------------------------------
_cache_instance: Optional[LLMCache] = None


def get_cache() -> LLMCache:  # noqa: D401
    global _cache_instance  # noqa: PLW0603
    if _cache_instance is None:
        _cache_instance = LLMCache()
    return _cache_instance 