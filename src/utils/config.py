from __future__ import annotations

"""YAML 配置加载器，带缓存。"""

from pathlib import Path
from functools import lru_cache
from typing import Any, Dict, Mapping, MutableMapping
import yaml  # type: ignore

_DEFAULT_PATH = Path("config/app_settings.yaml")


@lru_cache(maxsize=1)
def load_config(path: str | Path | None = None) -> Dict[str, Any]:  # noqa: D401
    """加载 YAML 配置并缓存。

    若文件不存在则返回空 dict。
    """
    cfg_path = Path(path) if path else _DEFAULT_PATH
    if not cfg_path.exists():
        return {}
    with cfg_path.open("r", encoding="utf-8") as f:
        data = yaml.safe_load(f) or {}

    # ------------------------------------------------------------------
    # Backward-compatibility shim: populate legacy ``llm`` subsection using
    # the new ``tasks`` layout so that older code continues to work.
    # ------------------------------------------------------------------
    _build_legacy_llm_view(data)
    return data


# ---------------------- New helper functions ----------------------

_TASK_CACHE: Dict[str, Any] = {}


def _build_legacy_llm_view(cfg: MutableMapping[str, Any]) -> None:  # noqa: D401
    """Populate a backward-compatible ``llm`` section.

    This keeps legacy code working after we migrated to the new
    ``llm_services`` + ``tasks`` layout (see config/app_settings.yaml).
    """
    if "llm" in cfg:  # existing section – nothing to do
        return

    tasks_cfg: Mapping[str, Mapping[str, Any]] = cfg.get("tasks", {})  # type: ignore[arg-type]

    legacy: Dict[str, Any] = {}
    # Common defaults taken from qa_answer task (if present)
    qa_ans = tasks_cfg.get("qa_answer", {})
    for key in ("model", "system_prompt", "max_words", "enable_format_extract"):
        if key in qa_ans:
            legacy[key] = qa_ans[key]

    # Map known task-specific blocks
    for name in (
        "sheet_summary",
        "file_summary",
        "intent_classification",
        "intent_level_probs",
        "embedding",
    ):
        if name in tasks_cfg:
            legacy[name] = tasks_cfg[name]

    # Attach to cfg so downstream code works unchanged
    cfg["llm"] = legacy


def get_task_cfg(task_name: str, force_reload: bool = False) -> Dict[str, Any]:  # noqa: D401
    """Return config for a given task, empty dict if missing."""
    global _TASK_CACHE
    if not _TASK_CACHE or force_reload:
        # 强制重新加载配置，避免使用过期缓存
        load_config.cache_clear()  # 清除 lru_cache
        _TASK_CACHE = load_config().get("tasks", {})  # type: ignore[assignment]
    return _TASK_CACHE.get(task_name, {}) 