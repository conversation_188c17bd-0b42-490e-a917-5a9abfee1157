"""全局组件注册表。"""
from __future__ import annotations

from typing import Dict, List

# 为避免循环引用，使用字符串注解

class ComponentRegistry:  # noqa: D101
    _pool: Dict[str, "type[object]"] = {}

    # ------------------------------------------------------------------
    # API
    # ------------------------------------------------------------------
    @classmethod
    def register(cls, comp_cls: "type[object]") -> None:  # noqa: D401
        """向注册表加入组件类。"""
        name = getattr(comp_cls, "name", None)
        if not name:
            raise ValueError("组件类必须具有唯一 name 属性")
        if name in cls._pool:
            raise ValueError(f"组件 '{name}' 已注册")
        cls._pool[name] = comp_cls  # type: ignore[assignment]

    @classmethod
    def get(cls, name: str) -> "type[object]":  # noqa: D401
        if name not in cls._pool:
            raise KeyError(f"组件 '{name}' 未注册")
        return cls._pool[name]

    @classmethod
    def list_names(cls) -> List[str]:  # noqa: D401
        return list(cls._pool.keys()) 