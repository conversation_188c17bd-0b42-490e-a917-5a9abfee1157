"""SQLGenerateComponent：根据 table_schemas + 问题生成 SQL 语句。"""
from __future__ import annotations

from typing import Any, Dict
import json
import re

from src.orchestration.base_component import BaseComponent, register_component
from src.parser.llm.client import ask, LLMClientError, _extract_json_block  # type: ignore


@register_component
class SQLGenerateComponent(BaseComponent):  # noqa: D101
    name = "sql_generate"

    _DEFAULT_PROMPT = (
        "你是资深数据分析师，请基于给定数据库表结构，生成满足用户需求的 SQLite SQL 语句。\n"
        "步骤：\n"
        "1. 思考需要查询哪些表、字段及过滤条件；\n"
        "2. 给出最终 SQL，只返回 JSON，如：{\"sql\": \"SELECT ...\"}。\n"
        "注意：不要包含注释，不要换行符。"
    )

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.llm_provider = config.get("provider", "mock")
        self.model = config.get("model")
        self.temperature = config.get("temperature", 0)
        self.prompt_tpl: str = config.get("prompt", self._DEFAULT_PROMPT)
        self.think: bool | None = config.get("think")

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        question: str = payload.get("query", "")
        schemas: Dict[str, Any] = payload.get("table_schemas", {})
        selected_table: str | None = payload.get("selected_table")
        if not question or not schemas:
            return {}

        # ------------------------------------------------------------------
        # MOCK 逻辑：尽量生成更贴合问题的 SQL
        if self.llm_provider == "mock":
            target_table = selected_table or next(iter(schemas), None)
            if not target_table:
                return {}

            columns = schemas[target_table].get("columns", []) if target_table in schemas else []
            col_names = [c.get("name", "") for c in columns]

            def find_col(keyword: str) -> str | None:
                for cn in col_names:
                    if keyword in cn.replace(" ", ""):
                        return cn
                return None

            col_item_name = find_col("数据项名称")
            col_encoding = find_col("数据元编码")

            # 如果问题问的是“银行机构代码的数据元编码是什么”，生成带过滤条件的查询
            if "银行机构代码" in question and col_item_name and col_encoding:
                sql_stmt = (
                    f"SELECT \"{col_encoding}\" FROM \"{target_table}\" "
                    f"WHERE \"{col_item_name}\" = '银行机构代码'"
                )
            else:
                # fallback: SELECT * LIMIT 10
                sql_stmt = f"SELECT * FROM \"{target_table}\" LIMIT 10"
            return {"sql": sql_stmt}

        schema_str = json.dumps(schemas, ensure_ascii=False)
        prompt = (
            f"{self.prompt_tpl}\n\n数据库 Schema：\n{schema_str}\n"
            + (f"已选表：{selected_table}\n" if selected_table else "")
            + f"\n用户问题：{question}\n"
            "请仅回复 JSON 对象，不要额外解释。"
        )
        try:
            raw = ask(
                prompt,
                provider=self.llm_provider,
                model=self.model,
                temperature=self.temperature,
                think=self.think,
                response_format={"type": "json_object"},
            )
            json_text = _extract_json_block(raw)
            data = json.loads(json_text)
            sql_stmt = str(data.get("sql", ""))
        except (LLMClientError, ValueError, json.JSONDecodeError):
            # fallback: simple heuristic
            sql_stmt = ""
            m = re.search(r"select .*", question, re.I)
            if m:
                sql_stmt = m.group(0)
        return {"sql": sql_stmt} 