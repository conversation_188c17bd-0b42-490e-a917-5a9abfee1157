from __future__ import annotations
import json
import logging
import sqlite3
from typing import Any, Dict, List, Tuple

from ..base_component import BaseComponent, register_component
from src.parser.llm.client import ask, LLMClientError


"""SheetNameClassifierComponent：调用大模型精确匹配用户问题对应的 sheet。

功能说明：
1. 读取 SQLite 数据库 `sheet_metadata` 表，获取所有 sheet_id 与 sheet_name。
2. 组装包含用户问题与全部表名的 prompt，调用 Ollama `qwen3:latest` 模型。
3. 期待模型返回 JSON：{"sheet_name": str, "confidence": float}。
   - 若 `confidence` 低于阈值或返回 "不确定"，则不干预现有流程，直接返回空字典。
   - 若模型确定表名，则输出：
       selected_table: 选中的表名
       sheet_ids: [f"sheet:{sheet_id}"]
   下游组件（如 SQL 生成、sheet_meta、mcp_query 等）即可使用这些字段，
   且其它召回组件仍可正常工作。
"""


@register_component
class SheetNameClassifierComponent(BaseComponent):  # noqa: D101
    """根据用户问题预测最相关的 sheet。"""

    name = "sheet_name_classifier"

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        """初始化：加载全部 sheet_name，用于 prompt 生成。"""
        self.db_path: str = str(config.get("db_path", "output/data.db"))
        self.provider: str | None = config.get("provider", "ollama")
        self.model: str | None = config.get("model", "qwen3:latest")
        # 判断置信度阈值，低于该值视为不确定
        self.min_conf: float = float(config.get("min_conf", 0.5))
        # 从配置读取 prompt 模板，支持占位符 {question} 与 {sheet_list}
        self.prompt_template: str = str(config.get("prompt", "")).strip()
        self.logger = logging.getLogger(self.name)

        # 预加载 sheet_id → sheet_name 映射，避免 run() 中反复查询
        self._sheet_map: Dict[int, str] = {}
        try:
            self._sheet_map = self._load_sheet_names()
            self.logger.info("加载 sheet_metadata 完成，共 %s 张表", len(self._sheet_map))
        except Exception as exc:  # noqa: BLE001
            self.logger.exception("加载 sheet_metadata 失败: %s", exc)

    # ------------------------------------------------------------------
    def _load_sheet_names(self) -> Dict[int, str]:  # noqa: D401
        """读取 sheet_metadata 表，返回 {sheet_id: sheet_name}."""
        conn = sqlite3.connect(self.db_path)
        cur = conn.execute("SELECT sheet_id, sheet_name FROM sheet_metadata")
        data = {int(row[0]): str(row[1]) for row in cur.fetchall() if row[1]}
        conn.close()
        return data

    # ------------------------------------------------------------------
    def _build_prompt(self, question: str) -> str:  # noqa: D401
        """根据配置的模板生成 prompt。"""
        # 生成表名列表字符串
        sheet_list: str = "\n".join(
            f"{idx}. {name}"
            for idx, (sid, name) in enumerate(sorted(self._sheet_map.items()), 1)
        )

        if self.prompt_template:
            try:
                return self.prompt_template.format(question=question, sheet_list=sheet_list)
            except KeyError as exc:  # pragma: no cover
                # 模板占位符错误，降级为简易 prompt
                self.logger.error("Prompt 模板占位符缺失: %s", exc)
        # ----------------- 默认降级 -----------------
        return f"用户问题：{question}\n表名列表：\n{sheet_list}"

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        question: str = payload.get("query", "").strip()
        if not question or not self._sheet_map:
            # 没有问题或表列表为空，直接跳过
            return {}

        prompt = self._build_prompt(question)

        try:
            resp = ask(
                prompt,
                provider=self.provider,
                model=self.model,
                think=False,  # 选择快速模式即可
                response_format={"type": "json_object"},
            )
        except LLMClientError as exc:
            # 已在 ask 内部记录警告，这里仅返回空结果
            self.logger.warning("LLM 调用失败，跳过 sheet_name 分类: %s", exc)
            return {}

        # ------------------ 解析模型返回 ---------------------------
        try:
            data = json.loads(resp)
        except json.JSONDecodeError:
            # 尝试提取 JSON block 再解析（ask 已做，但保守再尝试）
            try:
                import re as _re
                match = _re.search(r"\{[\s\S]*\}", resp)
                if match:
                    data = json.loads(match.group(0))
                else:
                    raise ValueError("无法解析 JSON")
            except Exception:
                self.logger.debug("无法解析模型返回，内容: %s", resp[:200])
                return {}

        sheet_name: str = str(data.get("sheet_name", "")).strip()
        confidence: float = float(data.get("confidence", 0))

        # 兼容模型返回 "不确定" 字样
        if sheet_name in ("", "不确定", "None") or confidence < self.min_conf:
            self.logger.info(
                "模型无法确定表名或置信度不足 (sheet_name=%s, confidence=%.2f)",
                sheet_name,
                confidence,
            )
            return {}

        # 校验表名存在
        target_items: List[Tuple[int, str]] = [
            (sid, name) for sid, name in self._sheet_map.items() if name == sheet_name
        ]
        if not target_items:
            self.logger.warning("模型返回未知表名: %s", sheet_name)
            return {}

        sheet_id, _ = target_items[0]
        self.logger.info(
            "SheetNameClassifier 命中: %s (sheet_id=%s, conf=%.2f)",
            sheet_name,
            sheet_id,
            confidence,
        )

        return {
            "selected_table": sheet_name,
            "sheet_ids": [f"sheet:{sheet_id}"],
        } 