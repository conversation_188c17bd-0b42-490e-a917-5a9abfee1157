"""IntentClassifier 组件。"""
from __future__ import annotations

from typing import Dict, Any

from ..base_component import BaseComponent, register_component

from dataclasses import asdict
from src.intent.classifier import IntentClassifier, IntentResult


@register_component
class IntentClassifierComponent(BaseComponent):  # noqa: D101
    name = "intent_classifier"

    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # 将流水线步骤中的配置传递给 IntentClassifier，避免依赖全局 app_settings.yaml
        self.cls = IntentClassifier(task_cfg=config or {})

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        question = payload.get("query", "")
        result: IntentResult = self.cls.classify(question)

        # 若识别为不相关，直接返回答案并终止后续步骤
        if result.intent == "unrelated":
            return {
                "intent": "unrelated",
                "answer": "不相关",
            }

        # ---------------- 向下游透传 min_prob 阈值 -------------------
        min_prob_cfg = self.config.get("min_prob")
        strategy_params = dict(result.params) if isinstance(result.params, dict) else {}
        if min_prob_cfg is not None:
            strategy_params["min_prob"] = float(min_prob_cfg)

        return {
            "intent": result.intent,
            "strategy": result.strategy,
            "strategy_params": strategy_params,
            # 转成普通 dict，方便 YAML 表达式或日志序列化
            "intent_result": asdict(result),
            "level_probs": result.level_probs,
        }

    # ------------------------------------------------------------------
    # Mock Output Normalization
    # ------------------------------------------------------------------
    def _process_mock_output(self, mock_ret: Dict[str, Any], _payload: Dict[str, Any]) -> Dict[str, Any]:  # noqa: D401
        """将 YAML 中简化形式的 mock_output 转换为完整输出结构。

        允许在配置中仅提供 ``{"file":0.5,"sheet":0.3,"row":0.2}`` 的概率分布，
        本方法会根据最大概率自动推断 intent 与 strategy 字段，
        并补全 ``strategy_params`` 与 ``level_probs`` 等下游依赖字段。
        这样即可在测试环境中简化 Mock 配置，同时保持与真实组件一致的返回格式，
        避免影响 CLI 的 Console 输出。"""

        # 若已含有 intent 字段，则认为已是完整格式，直接返回
        if "intent" in mock_ret:
            return mock_ret

        lv_file = float(mock_ret.get("file", 0.0))
        lv_sheet = float(mock_ret.get("sheet", 0.0))
        lv_row = float(mock_ret.get("row", 0.0))

        # 根据最大概率推断 intent
        probs = {"file": lv_file, "sheet": lv_sheet, "row": lv_row}
        intent = max(probs, key=probs.get)

        # 推断 strategy，与原实现保持命名一致
        strategy_map = {
            "file": "vector_file",
            "sheet": "vector_sheet",
            "row": "vector_row",
        }
        strategy = strategy_map.get(intent, "vector_row")

        # 继承 min_prob 配置，保持与真实运行时一致
        min_prob_cfg = self.config.get("min_prob")
        strategy_params: Dict[str, Any] = {}
        if min_prob_cfg is not None:
            strategy_params["min_prob"] = float(min_prob_cfg)

        # 组装完整返回结构
        return {
            "intent": intent,
            "strategy": strategy,
            "strategy_params": strategy_params,
            "level_probs": probs,
        } 