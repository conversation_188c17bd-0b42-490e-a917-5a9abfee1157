from __future__ import annotations
from typing import Dict, Any, List
import re
import logging

from ..base_component import BaseComponent, register_component

"""RerankComponent: 根据问题与已召回行文本做简单文本匹配排序。"""



@register_component
class RerankComponent(BaseComponent):  # noqa: D101
    name = "rerank"

    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.top_k: int = config.get("top_k", 5)
        # 新增：可选远程 Rerank 服务（SiliconFlow）
        self.provider: str | None = config.get("provider")
        self.model: str | None = config.get("model")
        self.api_key_env: str = config.get("api_key_env", "SILICONFLOW_API_KEY")
        # 允许通过步骤配置自定义 endpoint；若缺失则从 app_settings.yaml 读取
        self.endpoint: str | None = config.get("endpoint")
        if self.provider == "siliconflow" and not self.endpoint:
            from src.utils.config import load_config
            cfg = load_config()
            self.endpoint = (
                cfg.get("llm_services", {})
                .get("siliconflow", {})
                .get("rerank_endpoint", "https://api.siliconflow.cn/v1/rerank")
            )
        # 融合权重
        self.bm25_weight: float = float(config.get("bm25_weight", 1.3))
        self.vec_weight: float = float(config.get("vec_weight", 1.0))
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def _score(self, query: str, text: str, tokens: List[str]) -> int:  # noqa: D401
        # 以关键 token 匹配次数作为分数
        cnt = sum(text.count(tok) for tok in tokens)
        return cnt

    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        q: str = payload.get("query", "")
        rows: List[Dict[str, Any]] = payload.get("row_texts", [])
        if not q or not rows:
            return {}

        # ---------------- SiliconFlow rerank ----------------------
        if self.provider == "siliconflow" and self.model:
            try:
                import os
                import requests
                import json  # noqa: WPS433

                api_key = os.getenv(self.api_key_env)
                if not api_key:
                    raise RuntimeError(f"Missing API key env: {self.api_key_env}")

                docs = [r.get("text", "")[:2000] for r in rows]
                payload = {
                    "model": self.model,
                    "query": q,
                    "documents": docs,
                }
                resp = requests.post(
                    self.endpoint or "https://api.siliconflow.cn/v1/rerank",
                    headers={
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json",
                    },
                    data=json.dumps(payload),
                    timeout=15,
                )
                resp.raise_for_status()
                data = resp.json()
                scores = {int(res["index"]): float(res["relevance_score"]) for res in data.get("results", [])}
                # fallback if missing
                scored = [(scores.get(idx, 0.0), row) for idx, row in enumerate(rows)]
            except Exception as exc:  # noqa: BLE001
                self.logger.warning("Remote rerank failed, fallback to local: %s", exc)
                self.provider = None  # disable next time

        if self.provider != "siliconflow":
            tokens = [t for t in re.findall(r"[\u4e00-\u9fa5A-Za-z0-9]+", q) if len(t) > 1]
            scored = [
                (self._score(q, r.get("text", ""), tokens), r) for r in rows
            ]

        # 融合 bm25 分
        merged: list[tuple[float, Dict[str, Any]]] = []
        for base_score, row in scored:
            bm25 = float(row.get("_bm25", 0.0))
            composite = base_score * self.vec_weight + bm25 * self.bm25_weight
            merged.append((composite, row))

        merged.sort(key=lambda x: x[0], reverse=True)
        top_rows = [r for score, r in merged if score > 0][: self.top_k]
        if not top_rows:
            top_rows = [r for _, r in merged[: self.top_k]]  # fallback

        if top_rows:
            first_id = f"{top_rows[0]['sheet_id']}:{top_rows[0]['rowid']}" if 'sheet_id' in top_rows[0] else 'N/A'
        else:
            first_id = None
        self.logger.debug("Rerank selected %s rows, first=%s", len(top_rows), first_id)
        return {"row_texts": top_rows} 