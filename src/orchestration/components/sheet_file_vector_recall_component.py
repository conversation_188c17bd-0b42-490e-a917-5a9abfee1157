from __future__ import annotations

from typing import Dict, Any, List

from ..base_component import BaseComponent, register_component

from src.vectorization.vectorizer import Vectorizer
from src.vectorization.vector_store import ChromaVectorStore

import re

_CHINESE_RE = re.compile(r"[\u4e00-\u9fa5]+")


def _only_chinese(text: str) -> str:
    """保留中文字符，用于字符串匹配。"""
    return "".join(_CHINESE_RE.findall(text))


def _longest_common_substr_len(a: str, b: str) -> int:
    """返回 a 与 b 的最长公共子串长度。"""
    max_len = 0
    la, lb = len(a), len(b)
    for i in range(la):
        for j in range(i + 1, la + 1):
            sub = a[i:j]
            if len(sub) <= max_len:
                continue
            if sub in b:
                max_len = len(sub)
    return max_len


@register_component
class SheetFileVectorRecallComponent(BaseComponent):  # noqa: D101
    """仅负责 File / Sheet 两层向量召回。"""

    name = "sheet_file_vector_recall"

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        embed_model = config.get("embedding_model")
        self.vectorizer = Vectorizer(model=embed_model) if embed_model else Vectorizer()
        self.store_file = ChromaVectorStore(collection_name="file_vectors")
        self.store_sheet = ChromaVectorStore(collection_name="sheet_vectors")
        self.topk: int = config.get("topk", 3)
        # Sheet 描述过滤阈值
        self.min_substr_len: int = config.get("min_substr_len", 3)

        import logging
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        question: str = payload.get("query", "")
        if not question:
            return {}

        # ------------------ 短路：已锁定表范围 ------------------
        # 若上游组件（如 sheet_name_classifier）已提供精确的 sheet_ids，
        # 则无需再进行 File/Sheet 级向量召回，以免引入其他表噪声。
        if payload.get("sheet_ids"):
            self.logger.debug(
                "检测到上游 sheet_ids=%s，跳过 sheet_file_vector_recall", payload.get("sheet_ids")
            )
            return {}

        # 1) 生成查询向量
        vec: List[float] = self.vectorizer.embed_texts([question])[0]

        # 2) 分层向量召回 ------------------------------------------------
        candidates: Dict[str, float] = {}
        per_level_topk = self.topk

        # ---- File 层 ---------------------------------------------------
        try:
            file_ids = self.store_file.query(vec, top_k=per_level_topk, filter={"level": "file"})
        except Exception as ex:  # noqa: BLE001
            self.logger.warning("File level vector recall failed: %s", ex)
            file_ids = []

        # ---- Sheet 层 --------------------------------------------------
        try:
            sheet_ids_vec = self.store_sheet.query(vec, top_k=per_level_topk, filter={"level": "sheet"})
        except Exception as ex:  # noqa: BLE001
            self.logger.warning("Sheet level vector recall failed: %s", ex)
            sheet_ids_vec = []

        # 简单线性衰减得分（Sheet 层权重 1.0，File 层 0.5）
        for rank, rid in enumerate(file_ids):
            score = (per_level_topk - rank) / per_level_topk * 0.5
            candidates[rid] = max(candidates.get(rid, 0), score)
        for rank, rid in enumerate(sheet_ids_vec):
            score = (per_level_topk - rank) / per_level_topk * 1.0
            candidates[rid] = max(candidates.get(rid, 0), score)

        if not candidates:
            self.logger.warning("Vector recall found no candidates")
            return {"query_vector": vec}

        # 3) 取 TopK
        sorted_ids = sorted(candidates.items(), key=lambda x: x[1], reverse=True)
        final_ids = [rid for rid, _ in sorted_ids[: self.topk]]

        # 4) 获取元数据 --------------------------------------------------
        file_texts: List[Dict[str, Any]] = []
        sheet_meta: List[Dict[str, Any]] = []

        try:
            files = [rid for rid in final_ids if rid.startswith("file:")]
            sheets = [rid for rid in final_ids if rid.startswith("sheet:")]
            if files:
                ret = self.store_file.collection.get(ids=files, include=["metadatas"])  # type: ignore[attr-defined]
                for rid, meta in zip(ret.get("ids", []), ret.get("metadatas", [])):
                    if not meta:
                        continue
                    file_texts.append({
                        "file_id": meta.get("file_id"),
                        "key": meta.get("key"),
                        "text": meta.get("text", ""),
                    })
            if sheets:
                ret = self.store_sheet.collection.get(ids=sheets, include=["metadatas"])  # type: ignore[attr-defined]
                for rid, meta in zip(ret.get("ids", []), ret.get("metadatas", [])):
                    if not meta:
                        continue
                    pure_query = _only_chinese(question)
                    pure_embed = _only_chinese(str(meta.get("text", meta.get("description", ""))))[:30]
                    if _longest_common_substr_len(pure_query, pure_embed) < self.min_substr_len:
                        continue
                    sheet_meta.append({
                        "sheet_id": meta.get("sheet_id"),
                        "sheet_name": meta.get("sheet_name"),
                        "description": meta.get("text", meta.get("description")),
                    })
        except Exception as ex:  # noqa: BLE001
            self.logger.debug("Fetch metadata failed: %s", ex)

        # 5) 生成输出 -----------------------------------------------------
        sheet_ids = list({rid for rid in final_ids if rid.startswith("sheet:")})

        out: Dict[str, Any] = {
            "query_vector": vec,
            "sheet_ids": sheet_ids,
            "retrieved_ids": final_ids,
        }
        if file_texts:
            out["file_texts"] = file_texts
        if sheet_meta:
            out["sheet_meta"] = sheet_meta

        self.logger.info(
            "Sheet/File vector recall finished | final=%s sheet_ids=%s", len(final_ids), len(sheet_ids)
        )
        return out 