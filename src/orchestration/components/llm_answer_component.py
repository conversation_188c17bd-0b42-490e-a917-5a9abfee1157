"""调用大模型总结回答的组件。"""
from __future__ import annotations

from typing import Dict, Any, List 
import json
import logging

from ..base_component import BaseComponent, register_component
from ..answer_llm import summarize_answer
from src.utils.config import get_task_cfg


# ----------------------------------------------------------------------
# 公共辅助函数：从文本中提取 JSON 答案
# ----------------------------------------------------------------------


def extract_answer_from_json(text: str) -> str | None:  # noqa: D401
    """从任意文本中提取 JSON 中的 answer 字段。

    支持以下格式：
    1) 纯 JSON，例如 {"answer": "..."}
    2) markdown 代码块 ```json\n{...}\n```
    3) 文本包裹 JSON，只要能找到第一个 "{" 和最后一个 "}"。
    返回提取到的 answer 字符串，若失败返回 None。
    """

    import re
    import json as _json

    if not text:
        return None

    # 1. 尝试剥离 markdown 代码块
    md_match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", text, flags=re.IGNORECASE)
    if md_match:
        text = md_match.group(1).strip()

    # 2. 若仍无纯 JSON 外壳，截取第一个 '{' 到最后一个 '}'
    if not (text.strip().startswith("{") or text.strip().startswith("[")):
        first = text.find("{")
        last = text.rfind("}")
        if first != -1 and last != -1 and last > first:
            text = text[first : last + 1]

    # 3. 解析 JSON
    try:
        data_obj = _json.loads(text)
    except Exception:
        return None

    if isinstance(data_obj, dict):
        ans = str(data_obj.get("answer") or data_obj.get("答案") or "").strip()
        return ans or None

    if isinstance(data_obj, list):
        return _json.dumps(data_obj, ensure_ascii=False, indent=2)

    return None

@register_component
class LLMAnswerComponent(BaseComponent):  # noqa: D101
    name = "llm_answer"

    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # 读取全局 YAML 设置并让流水线步骤配置覆盖全局
        qa_cfg = {**get_task_cfg("qa_answer"), **(config or {})}
        self.enable_summary: bool = qa_cfg.get("enable", True)

        # 是否启用"格式:"字段的专用抽取逻辑，默认关闭
        self.enable_format_extract: bool = qa_cfg.get("enable_format_extract", False)
        # 通用 system prompt（若未配置则回退到 summarize_answer 中的默认值）
        self.default_system_prompt: str | None = qa_cfg.get("system_prompt")

        # 控制是否启用大模型 Function-Calling 调用外部工具能力
        self.enable_tool_calling: bool = qa_cfg.get("enable_tool_calling", True)

        # 保存 provider/model 供 run() 调用
        self._provider = qa_cfg.get("provider")
        self._model = qa_cfg.get("model")
        self._think = qa_cfg.get("think")
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        if not self.enable_summary:
            return {}
        question = payload.get("query", "")

        # ------------------------------------------------------------
        # 新增：用于收集所有 MCP 调用的记录，供上游 Console 打印
        # ------------------------------------------------------------
        call_records: List[Dict[str, Any]] = []  # 每条记录: {name:str, args:dict, result:str}

        # ------------------------------------------------------------------
        # 从上游组件读取已识别的表名 / 字段名，供 LLM 决定是否调用 MCP
        # ------------------------------------------------------------------
        table_name: str | None = payload.get("table_name")  # 由 DataItemIdentifier 写入
        field_name: str | None = payload.get("field_name") or payload.get("identified_data_item")
        # 不再做自动血缘查询，完全交由大模型通过 Function-Calling 决定是否调用 MCP
        row_texts = payload.get("row_texts", [])
        file_texts = payload.get("file_texts", [])
        sql_result = payload.get("sql_result")
        sheet_meta = payload.get("sheet_meta", [])  # List[Dict]
        column_texts = payload.get("column_texts", [])  # List[Dict]

        # ---- 新增：根据 level_probs 动态调整上下文顺序 ----------------------
        level_probs: Dict[str, float] = payload.get("level_probs", {})  # type: ignore[assignment]
        if not level_probs:
            # 默认分布，与 VectorRecall 中保持一致
            level_probs = {"file": 0.1, "sheet": 0.1, "row": 0.8}

        # 只保留概率 >= 0.3 的层级，并按概率降序排列
        sorted_levels = [lvl for lvl, prob in sorted(level_probs.items(), key=lambda x: x[1], reverse=True) if prob >= 0.3]
        if not sorted_levels:
            # 若全部低于阈值，则回退全部层级（保持原顺序）
            sorted_levels = ["row", "sheet", "file"]

        # --- 组装各层级上下文 ------------------------------------------------
        ctx_by_level: Dict[str, List[str]] = {"file": [], "sheet": [], "row": []}

        # File 级文本
        for ft in file_texts:
            ctx = ft.get("text", "")
            if ctx:
                ctx_by_level["file"].append(f"[file {ft.get('file_id')}] {ctx}")

        # Sheet 级信息 (元数据 + 列文本 + 表预览)
        for meta in sheet_meta:
            desc = meta.get("description") or "(无描述)"
            ctx_by_level["sheet"].append(f"[sheet {meta.get('sheet_id')}] {desc}")

        for col in column_texts:
            col_ctx = col.get("text") or ""
            if col_ctx:
                ctx_by_level["sheet"].append(col_ctx)

        # 不再将表预览信息写入上下文

        # Row 级文本
        if row_texts:
            row_snippets = [f"[sheet {r['sheet_id']} row {r['rowid']}] {r['text']}" for r in row_texts]
            ctx_by_level["row"].extend(row_snippets)

        # 按层级概率顺序拼接上下文
        context_parts: List[str] = []
        # 若上游已识别表/字段，则加入上下文，同时附带查询结果（便于回答亦能触发 MCP 调用打印）
        if table_name and field_name:
            # 仅将表名/字段名放入上下文，方便大模型自行决定是否调用 MCP
            context_parts.append(f"已识别表: {table_name}，字段: {field_name}")

        # 若有 SQL 查询结果，放在最前，便于直接回答
        if sql_result:
            context_parts.append(f"SQL 查询结果：\n{sql_result}")

        for lvl in sorted_levels:
            context_parts.extend(ctx_by_level.get(lvl, []))

        # 如果仍为空，则回退原流程
        if not context_parts:
            context_parts = ctx_by_level["sheet"] + ctx_by_level["row"] + ctx_by_level["file"]

        context = "\n".join(context_parts) or "(无上下文)"

        # ------------------------------------------------------------------
        # 【新增】Function-Calling + MCP：将 query_lineage 作为工具注册给大模型
        # ------------------------------------------------------------------
        # ------------------------------------------------------------------
        # MCP 工具：通过 FastMCP Server 动态发现，无需手动维护元数据
        # ------------------------------------------------------------------
        from src.mcp.service import query_lineage, list_mcp_services, clean_tools_for_api  # 仅导入客户端函数

        # ------------------------------------------------------------------
        # 预先获取 MCP 工具列表，拼接到上下文，并在后续调用中禁止模型再次调用 list_mcp_services
        # ------------------------------------------------------------------
        _MCP_TOOLS: list[dict] = []
        _tools_json_str: str = "[]"
        try:
            _tools_json_str = list_mcp_services()
            parsed_tools = json.loads(_tools_json_str)
            if isinstance(parsed_tools, list):
                # 移除 list_mcp_services，避免模型重复调用
                _MCP_TOOLS = [t for t in parsed_tools if t.get("name") != "list_mcp_services"]
            self.logger.info("Final MCP tools prepared for LLM: %s", json.dumps(_MCP_TOOLS, ensure_ascii=False))
        except Exception as e:
            self.logger.error("Failed to get or parse MCP tools: %s", e, exc_info=True)
            _MCP_TOOLS = []  # 确保失败时为空列表

        import requests
        import os
        import uuid

        # ------------------------------------------------------------------
        # Helper: 通用 Function-Calling 请求
        # ------------------------------------------------------------------

        def _chat_with_tools(
            *,
            provider: str,
            model: str,
            system_msg: str,
            user_msg: str,
            tools: list[dict],
            messages: list[dict] | None = None,
            tool_choice: str = "auto",  # "auto" / "none"
            timeout_sec: float = 60.0,
        ) -> dict:  # noqa: D401
            """向指定 provider 发送带 tools 的 chat 请求，返回 JSON response。"""  # noqa: D401
            provider = provider.lower()

            if messages is None:
                messages = [
                    {"role": "system", "content": system_msg},
                    {"role": "user", "content": user_msg},
                ]

            # 清理工具格式以兼容不同API
            cleaned_tools = clean_tools_for_api(tools, provider=provider) if tools else []

            payload: dict = {
                "model": model,
                "messages": messages,
                "tool_choice": tool_choice,
                "stream": False,
                "temperature": 0.0,
            }
            # ---- 确保 messages.content 为纯字符串，避免 TextContent 无法序列化 ----
            for _msg in payload["messages"]:
                if "content" in _msg and not isinstance(_msg["content"], str):
                    _msg["content"] = str(_msg["content"])
            if cleaned_tools:
                payload["tools"] = cleaned_tools
            # 若未提供工具则可以要求 JSON 输出
            if not tools:
                payload["response_format"] = {"type": "json_object"}

            # ---------------- 从全局 YAML 读取 provider 具体配置 ----------------
            from src.utils.config import load_config  # 本地导入避免循环依赖

            services_cfg = load_config().get("llm_services", {})
            prov_cfg: dict = services_cfg.get(provider, {})  # type: ignore[assignment]

            if not prov_cfg:
                raise RuntimeError(f"未在 app_settings.yaml 的 llm_services 找到 provider={provider}")

            headers: dict[str, str] = {"Content-Type": "application/json"}
            proxies = None  # 默认不走代理

            # ==== Ollama（本地部署）====
            if provider == "ollama":
                base = prov_cfg.get("host")
                # 确保带 /api/chat 路径
                url = base if base.endswith("/api/chat") else base.rstrip("/") + "/api/chat"
                # 本地 Ollama 无鉴权、禁用代理
                proxies = {"http": None, "https": None}

            # ==== 其他云服务（SiliconFlow, Gientech 等）====
            else:
                url = prov_cfg.get("endpoint")
                if not url:
                    raise RuntimeError(f"llm_services.{provider}.endpoint 未配置")

                # 若配置了 api_key_env，则从环境变量读取
                key_env = prov_cfg.get("api_key_env")
                if key_env:
                    api_key = os.getenv(key_env)
                    if not api_key:
                        raise RuntimeError(f"缺少 {key_env}")
                    headers["Authorization"] = f"Bearer {api_key}"

                # Gientech 需要附加 think=True
            if provider == "gientech":
                payload.update({"think": self._think})

            # 记录 LLM 请求
            import logging as _logging
            _llm_logger = _logging.getLogger("llm_traffic")
            # 某些对象(如 pydantic TextContent) 不是 JSON 可序列化；日志打印时回退 str()
            try:
                _llm_logger.info("LLM request payload (provider=%s):\n%s", provider, json.dumps(payload, ensure_ascii=False, indent=2, default=str))
            except TypeError:
                _llm_logger.info("LLM request payload (provider=%s) 组装完毕 (包含不可序列化对象，已隐藏)", provider)
            
            # 添加调试日志，记录清理后的tools结构
            if tools:
                _llm_logger.debug("清理前tools: %s", json.dumps(tools, ensure_ascii=False))
                _llm_logger.debug("清理后tools: %s", json.dumps(cleaned_tools, ensure_ascii=False))

            resp = requests.post(url, headers=headers, json=payload, timeout=timeout_sec, proxies=proxies)
            
            # 记录完整的响应状态和错误信息
            if resp.status_code != 200:
                _llm_logger.error("LLM API 错误: HTTP %d\n响应: %s", resp.status_code, resp.text)
                # 如果是400错误，可能是参数问题，记录详细信息
                if resp.status_code == 400:
                    _llm_logger.error("参数错误，请检查payload格式: %s", json.dumps(payload, ensure_ascii=False))
                raise Exception(f"LLM API返回错误: {resp.status_code}, {resp.text}")
            
            # 记录 LLM 响应（只打印前 500 字，避免日志过大）
            try:
                _llm_logger.info("LLM response:\n%s", str(resp.text))
            except Exception:  # noqa: BLE001
                pass
            return resp.json()

        # -------- 尝试通用 Function-Calling 流程：若失败即回退 --------

        self.logger.debug(
            "LLM summarize start | question=%s row_texts=%s",
            question,
            len(row_texts),
        )

        # ------------------------------------------------------------------
        # 1) 允许 LLM 直接返回结构化 JSON：{"answer": str, "show_preview": bool, "preview_tables": []}
        # 2) 向 ask() 传入 response_format，Ollama 会自动加上 format=json 字段 [[memory:3397493]].
        # 3) 解析失败则回退到 summarize_answer 的纯文本路径。
        # ------------------------------------------------------------------

        if self.enable_tool_calling:
            system_msg = (
                self.default_system_prompt
                or (
                    "你是一位中文数据分析助手，具备调用外部 MCP 工具的能力。\n"
                    "已在上下文中提供所有 MCP 工具列表，请仔细分析用户提出的问题，谨慎判断是否需要调用工具回答问题。\n"
                    "若调用工具，请严格返回 JSON。\n"
                    "若不需要调用工具，则回答格式为: {\"answer\": <回答内容>} \n"
                    "<回答内容>不要为空，除了json也不要输出其他内容。\n"
                )
            )
            # 将可用工具列表放入 prompt 上下文，避免模型再次请求
            user_msg = (
                f"用户问题：\n{question}\n\n候选上下文：\n{context[:120*512]}\n"
            )

            try:
                # 对话上下文初始化
                messages_ctx: list[dict] = [
                    {"role": "system", "content": system_msg},
                    {"role": "user", "content": user_msg},
                ]

                answer_text: str = ""

                # --------------------------- 首轮：允许调用 MCP 工具 ---------------------------
                rsp = _chat_with_tools(
                    provider=self._provider,
                    model=self._model,
                    system_msg=system_msg,
                    user_msg=user_msg,
                    tools=_MCP_TOOLS,
                    messages=messages_ctx,
                )

                assistant_msg = (
                    rsp.get("message", {})
                    if self._provider == "ollama"
                    else rsp.get("choices", [{}])[0].get("message", {})
                )

                # ---------- 解析工具调用（兼容 JSON 放在 content 的形式） ----------
                content_str = assistant_msg.get("content", "")
                tool_calls = assistant_msg.get("tool_calls") or []

                def _try_parse_json_obj(text: str) -> list[dict]:  # noqa: D401
                    """从字符串中提取包含 name / parameters 的 JSON 对象列表。"""
                    results: list[dict] = []
                    try:
                        obj = json.loads(text)
                    except json.JSONDecodeError:
                        return []

                    iterable = obj if isinstance(obj, list) else [obj]
                    for itm in iterable:
                        if (
                            isinstance(itm, dict)
                            and "name" in itm
                            and ("parameters" in itm or "arguments" in itm)
                        ):
                            _args_obj = itm.get("parameters") if "parameters" in itm else itm.get("arguments")
                            results.append(
                                {
                                    "id": itm.get("id") or str(uuid.uuid4()),
                                    "function": {
                                        "name": itm["name"],
                                        "arguments": json.dumps(_args_obj, ensure_ascii=False),
                                    },
                                }
                            )
                    return results

                if not tool_calls and content_str:
                    # 1) 先去掉 OpenAI function 调用尾部标记，例如 <|tool_call_end|>
                    cleaned = content_str.split("<|", 1)[0].strip()

                    # 2) 尝试整体解析
                    tool_calls = _try_parse_json_obj(cleaned)

                    # 3) 整体解析失败，再逐行解析
                    if not tool_calls:
                        multi_calls: list[dict] = []
                        for _ln in (ln.strip() for ln in cleaned.splitlines() if ln.strip()):
                            multi_calls.extend(_try_parse_json_obj(_ln))
                        if multi_calls:
                            tool_calls = multi_calls

                # ---------- 情况 1：模型直接回答，无需工具 ----------
                if not tool_calls:
                    self.logger.debug("无工具调用，模型直接给出答案。")
                    answer_text = assistant_msg.get("content", "")
                else:
                    # ---------- 情况 2：模型调用了工具，执行并收集结果 ----------
                    tool_msgs: list[dict] = []
                    for tc in tool_calls:
                        tc_id = tc.get("id") or str(uuid.uuid4())
                        fn_name = tc.get("function", {}).get("name")
                        args_json = tc.get("function", {}).get("arguments", "{}")
                        try:
                            fn_args = json.loads(args_json)
                        except Exception:
                            fn_args = {}

                        tool_result: str
                        if fn_name == "query_lineage":
                            if ("table" not in fn_args or "field" not in fn_args) and table_name and field_name:
                                fn_args = {"table": table_name, "field": field_name}
                            tool_result = query_lineage(**fn_args)
                        elif fn_name == "list_mcp_services":
                            # 该工具无需参数
                            tool_result = list_mcp_services()
                        # 若未来新增工具，可在此按需扩展对应函数调用
                        else:
                            tool_result = f"未知工具 {fn_name}"

                        self.logger.info(
                            "[LLMToolCall] name=%s args=%s result=%s",
                            fn_name,
                            fn_args,
                            tool_result,
                        )

                        # ------------------- 记录调用详情 --------------------
                        call_records.append({
                            "name": fn_name,
                            "args": fn_args,
                            "result": tool_result,
                        })

                        tool_msgs.append({
                            "role": "tool",
                            "tool_call_id": tc_id,
                            "name": fn_name,
                            "content": tool_result,
                        })

                    # ---------- 第二轮：关闭工具，要求生成最终答案 ----------
                    final_system_prompt = (
                        "工具调用结果已返回，请基于 TextContent 结果用中文回答用户问题，不要再调用任何工具。\n"
                        "优先采用调用返回的结果，把调用结果和用户问题结合起来形成一段通顺、自然的文字回答。\n"
                        "请严格仅输出一个 JSON 对象，格式: {\"answer\": <回答内容>} \n"
                        "<回答内容>不要为空，除了json也不要输出其他内容。\n"
                    )

                    messages_final: list[dict] = [
                        {"role": "system", "content": final_system_prompt},
                        {"role": "user", "content": "用户问题：" + question},
                        assistant_msg,
                        *tool_msgs,
                    ]

                    rsp_final = _chat_with_tools(
                        provider=self._provider,
                        model=self._model,
                        system_msg=final_system_prompt,
                        user_msg=question,
                        tools=[],  # 禁止再次调用工具
                        messages=messages_final,
                        tool_choice="none", # 强制模型不能再提出函数调用
                    )

                    final_msg = (
                        rsp_final.get("message", {})
                        if self._provider == "ollama"
                        else rsp_final.get("choices", [{}])[0].get("message", {})
                    )
                    answer_text = final_msg.get("content", "")

                # ask() 可能已抽取 json block，但仍用 json.loads 解析
                try:
                    data = json.loads(answer_text)
                except json.JSONDecodeError:
                    data = None

                if isinstance(data, dict):
                    # 兼容模型返回的中文字段名 "答案"
                    ans_text = str(data.get("answer", "")).strip()
                    if not ans_text and "答案" in data:
                        ans_text = str(data.get("答案", "")).strip()
                    out: Dict[str, Any] = {"answer": ans_text, "mcp_calls": call_records}

                    return out
                elif isinstance(data, list):
                    # 若返回列表（通常为多个血缘条目），直接 pretty-print 作为答案
                    ans_text = json.dumps(data, ensure_ascii=False, indent=2)
                    return {"answer": ans_text, "mcp_calls": call_records}
                else:
                    # 不是 dict（可能是 list 或直接文本），直接返回整体文本
                    ans_text = answer_text.strip()
                    # 尝试再从中解析 JSON answer
                    parsed_ans = extract_answer_from_json(ans_text)
                    if parsed_ans is not None:
                        ans_text = parsed_ans
                    return {"answer": ans_text, "mcp_calls": call_records}
            except (json.JSONDecodeError) as ex:
                self.logger.warning("LLM structured answer failed, fallback to plain summarize: %s", ex)

        # ------------------ Fallback：纯文本 summarize_answer ------------------
        # 显式传入 provider / model，确保使用步骤级配置
        answer = summarize_answer(
            question,
            context,
            provider=self._provider,
            model=self._model,
        ).strip()

        # -------------------- 后处理：移除表预览 --------------------------
        try:
            import re as _re

            def _filter_table_lines(text: str) -> str:  # noqa: D401
                """去除 markdown 或 rich 样式表格行，保留正常文本。"""
                filtered: list[str] = []
                for ln in text.splitlines():
                    # box drawing / rich 表格行
                    if _re.match(r"^\s*[┏┓┗┛┡┩┻┳┣┫┨┠╇╈╤╧╪─━│┃╭╮╯╰]", ln):
                        continue
                    # markdown 表格行（以 | 分隔并至少包含两个竖线）
                    if _re.match(r"^\s*\|.*\|\s*$", ln):
                        continue
                    filtered.append(ln)
                return "\n".join(filtered).strip()

            cleaned_answer = _filter_table_lines(answer)
            if cleaned_answer:
                answer = cleaned_answer
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("strip table preview failed: %s", _ex)

        self.logger.info("LLM summarize finished | answer_length=%s", len(answer))
        if answer:
            # fallback 模型可能返回 JSON，尝试解析
            parsed_ans = extract_answer_from_json(answer)
            if parsed_ans is not None:
                answer = parsed_ans
            return {"answer": answer, "mcp_calls": call_records}
        return {} 