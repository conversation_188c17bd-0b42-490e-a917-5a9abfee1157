from __future__ import annotations
from typing import Dict, Any, List
import logging
import sqlite3

from ..base_component import BaseComponent, register_component


"""RowBM25RecallComponent

基于 SQLite FTS5 的 BM25 检索，对 row_vectors.text 进行关键词召回。
可与 RowVectorRecallComponent 组合形成双路召回。

配置示例 (qa_pipeline.yaml)::

  - name: row_bm25_recall
    config:
      enabled: true        # 置为 false 表示跳过
      db_path: output/data.db
      top_k_rows: 50       # FTS5 召回数量
      top_n_rows: 15       # 最终返回行数 (merge 时生效)
      boost_existing: 0.2  # 若与上游 row_texts 重复, 额外加分
"""

@register_component
class RowBM25RecallComponent(BaseComponent):  # noqa: D101
    name = "row_bm25_recall"

    # --------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.enabled: bool = bool(config.get("enabled", True))
        if not self.enabled:
            return

        self.db_path: str = str(config.get("db_path", "output/data.db"))
        self.top_k_rows: int = int(config.get("top_k_rows", 50))
        self.top_n_rows: int = int(config.get("top_n_rows", 15))
        self.boost_existing: float = float(config.get("boost_existing", 0.2))
        # 文档长度归一化参数，越大惩罚越轻；近似平均长度
        self.len_norm_div: int = int(config.get("len_norm_div", 300))

        self.logger = logging.getLogger(self.name)

        # 连接 SQLite，row_factory 便于 dict 输出
        self._conn = sqlite3.connect(self.db_path)
        self._conn.row_factory = sqlite3.Row

    # --------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        if not self.enabled:
            return {}

        # 只在 intent == "row" 时参与
        if payload.get("intent") != "row":
            return {}

        query: str = payload.get("query", "").strip()
        # ------------------- 关键词优先 -----------------------------
        keywords: List[str] = payload.get("keywords", [])
        match_q: str = query  # 默认用原始查询
        if keywords:
            clean_tokens = [t.strip() for t in keywords if t.strip()]
            if clean_tokens:
                # 用 OR 连接，单词用引号包围以支持中文短语
                match_q = " OR ".join(f'"{tok}"' for tok in clean_tokens)

        self.logger.debug("BM25 recall start | match_q=%s", match_q)

        if not query:
            return {}

        # ------------------- FTS 检索 -------------------------------
        cur = self._conn.cursor()

        # 使用 FTS5 bm25() 评分；若表不存在则直接返回空
        try:
            cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='row_vectors_fts'")
            if cur.fetchone() is None:
                self.logger.warning("row_vectors_fts 不存在, BM25 召回跳过")
                return {}

            # 匹配语法：直接用查询字符串, FTS5 自动分词；
            # 为防止 SQL 注入，仅使用参数化占位符
            sql = (
                "SELECT rv.sheet_id, rv.rowid AS rid, rv.text, bm25(row_vectors_fts) AS score "
                "FROM row_vectors_fts JOIN row_vectors rv ON row_vectors_fts.src_id = rv.rowid "
                "WHERE row_vectors_fts MATCH ? ORDER BY score LIMIT ?"
            )
            cur.execute(sql, (match_q, self.top_k_rows))
            rows = cur.fetchall()
        except Exception as exc:  # noqa: BLE001
            self.logger.error("BM25 查询失败: %s", exc)
            return {}

        if not rows:
            self.logger.info("BM25 recall none, early exit")
            return {}

        # 如果现有行都来自同一张表，则只保留该表的 BM25 结果，减少跨表噪声
        target_sheet: int | None = None
        existing_rows: List[Dict[str, Any]] = payload.get("row_texts", [])
        if existing_rows:
            sheets = {r.get("sheet_id") for r in existing_rows if r.get("sheet_id") is not None}
            if len(sheets) == 1:
                target_sheet = next(iter(sheets))

        # 分数越小越好 (bm25)，转换为相似度分 (越大越好)
        bm25_rows: List[Dict[str, Any]] = []
        for r in rows:
            if target_sheet is not None and r["sheet_id"] != target_sheet:
                continue  # 过滤掉其它表
            bm25_score = float(r["score"])
            # ----------- 长文本惩罚 ---------------------
            length_penalty = max(1.0, len(r["text"]) / self.len_norm_div)
            norm_score = bm25_score * length_penalty

            # FTS5 bm25() 分值越小越相关；常见情况相关文本会得到负分。
            # 将分值映射到 (0, 2] 区间：
            #   正分   →  0~1   （分值越大，相似度越低）
            #   负分   →  1~2   （绝对值越大，相似度越高）
            if norm_score >= 0:
                sim = 1.0 / (1.0 + norm_score)
            else:
                sim = 1.0 + abs(norm_score) / (1.0 + abs(norm_score))
            bm25_rows.append(
                {
                    "sheet_id": r["sheet_id"],
                    "rowid": r["rid"],
                    "text": r["text"],
                    "_bm25": sim,
                }
            )

        # ------------------- 合并到现有行 ---------------------------
        merged: Dict[tuple, Dict[str, Any]] = {}

        # 先放入已有结果
        for r in existing_rows:
            key = (r.get("sheet_id"), r.get("rowid"))
            merged[key] = {**r, "_bm25": 0.0}

        # 合并 BM25 结果
        for r in bm25_rows:
            key = (r["sheet_id"], r["rowid"])
            if key in merged:
                # 已存在, 叠加得分以提升排名
                merged[key]["_bm25"] = max(merged[key].get("_bm25", 0.0), r["_bm25"]) + self.boost_existing
            else:
                merged[key] = r

        # 排序: 先按 _bm25 下降, 再保留向量召回的顺序
        # 如果原行里有 _score 字段，综合考虑
        def _rank(row: Dict[str, Any]) -> float:
            vec_score = float(row.get("_score", 0.0))
            bm25_score = float(row.get("_bm25", 0.0))
            return vec_score + bm25_score

        final_rows = sorted(merged.values(), key=_rank, reverse=True)[: self.top_n_rows]

        self.logger.info(
            "BM25 recall merged | bm25=%s existing=%s final=%s",
            len(bm25_rows), len(existing_rows), len(final_rows),
        )

        return {"row_texts": final_rows} 