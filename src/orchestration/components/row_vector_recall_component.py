from __future__ import annotations
from typing import Dict, Any, List
import logging

from ..base_component import BaseComponent, register_component
from src.vectorization.vectorizer import embed_texts
from src.vectorization.vector_store import ChromaVectorStore


"""RowVectorRecallComponent：在 intent == 'row' 场景下，一次性完成行级向量召回、
   sheet 相关性评估以及行重排。

   1. 计算 query embedding → row_vectors & sheet_vectors 分别召回。
   2. 取 top-k 行向量 (含 text 元数据)，无需再访问 SQLite。
   3. 找到最相关 sheet_id，将属于该 sheet 的行排在最前，再按距离升序。
   4. 最终返回前 top_n 行文本供下游 (rerank / llm_answer) 直接使用。
"""


@register_component
class RowVectorRecallComponent(BaseComponent):  # noqa: D101
    name = "row_vector_recall"

    kw_weight: float

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.top_k_rows: int = int(config.get("top_k_rows", 20))
        self.top_n_rows: int = int(config.get("top_n_rows", 5))
        # collection 名称可通过配置覆盖
        self.row_collection: str = str(config.get("row_collection", "row_vectors"))
        self.sheet_collection: str = str(config.get("sheet_collection", "sheet_vectors"))
        self.logger = logging.getLogger(self.name)

        # 关键词权重系数 (0-1)
        self.kw_weight = float(config.get("kw_weight", 0.3))

        # 初始化 VectorStore（不在 run() 内反复构造，节约开销）
        self._row_vs = ChromaVectorStore(collection_name=self.row_collection)
        self._sheet_vs = ChromaVectorStore(collection_name=self.sheet_collection)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        # ---------------------- 快速跳过逻辑 ----------------------------
        # 若上游组件（如 DataItemIdentifier 快速通道）已提供行文本，则无需再进行向量召回。
        if isinstance(payload.get("row_texts"), list) and payload["row_texts"]:
            self.logger.debug(
                "RowVectorRecallComponent: 检测到已有 row_texts (len=%s)，跳过向量召回。",
                len(payload["row_texts"]),
            )
            return {}

        # 仅当 intent == 'row' 时执行，其他场景直接跳过，不干扰现有流程。
        if payload.get("intent") != "row":
            return {}

        question: str = payload.get("query", "")
        if not question:
            self.logger.warning("query 为空，RowVectorRecallComponent 跳过执行")
            return {}

        # 1) embed query
        embedding = embed_texts([question])[0]
        if not embedding:
            self.logger.error("生成 query embedding 失败，RowVectorRecallComponent 中止")
            return {}

        # 2) 表名匹配预筛选 + row 向量召回
        # 提取表名关键词
        def _extract_table_keywords(query: str) -> list[str]:
            """从查询中提取可能的表名关键词"""
            import re
            # 常见的表名模式
            table_patterns = [
                r"(\w*表)",  # 以"表"结尾的词
                r"(\w*信息表)",  # 以"信息表"结尾的词
                r"(\w*业务\w*表)",  # 包含"业务"的表名
                r"(\w*余额表)",  # 以"余额表"结尾的词
                r"(\w*明细表)",  # 以"明细表"结尾的词
            ]

            keywords = []
            for pattern in table_patterns:
                matches = re.findall(pattern, query)
                keywords.extend(matches)

            # 去重并过滤掉过短的关键词
            keywords = list(set([kw for kw in keywords if len(kw) >= 3]))
            return keywords

        table_keywords = _extract_table_keywords(question)
        self.logger.debug("Extracted table keywords from query: %s", table_keywords)

        # 如果提取到了表名关键词，先进行表名匹配筛选
        if table_keywords:
            self.logger.debug("Performing table-name-first recall strategy")

            # 先尝试直接按表名筛选所有行，不依赖向量相似度
            # 这样可以确保所有目标表的行都被考虑，不受查询字符串变化影响
            try:
                # 获取所有行的元数据，按表名筛选
                all_count = self._row_vs.collection.count()
                self.logger.debug("Attempting to get all %d rows for table name filtering", all_count)

                # 分批获取所有行的元数据（避免内存问题）
                batch_size = 1000
                all_target_rows = []

                for offset in range(0, all_count, batch_size):
                    batch_size_actual = min(batch_size, all_count - offset)
                    batch_ret = self._row_vs.collection.get(
                        limit=batch_size_actual,
                        offset=offset,
                        include=["metadatas"]
                    )

                    if batch_ret.get("ids"):
                        batch_ids = batch_ret["ids"]
                        batch_metas = batch_ret["metadatas"]

                        # 筛选包含目标表名的行
                        for rid, meta in zip(batch_ids, batch_metas):
                            text = meta.get("text", "")
                            for keyword in table_keywords:
                                table_name_pattern = f"表名:CBRC_EAST5_0_业务Mapping_V2_2_4_{keyword}"
                                if table_name_pattern in text:
                                    all_target_rows.append(rid)
                                    break

                self.logger.debug("Found %d target table rows by direct filtering", len(all_target_rows))

                if all_target_rows:
                    # 直接获取这些目标行的详细信息，然后计算向量距离
                    self.logger.debug("Getting details for %d target table rows", len(all_target_rows))

                    # 获取目标行的详细信息
                    target_rows_data = self._row_vs.collection.get(
                        ids=all_target_rows,
                        include=["metadatas", "embeddings"]
                    )

                    if target_rows_data.get("ids"):
                        # 计算查询向量与目标行向量的距离
                        import numpy as np
                        target_ids = target_rows_data["ids"]
                        target_metas = target_rows_data["metadatas"]
                        target_embeddings = target_rows_data["embeddings"]

                        distances = []
                        for target_embedding in target_embeddings:
                            # 计算余弦距离
                            dot_product = np.dot(embedding, target_embedding)
                            norm_query = np.linalg.norm(embedding)
                            norm_target = np.linalg.norm(target_embedding)
                            cosine_similarity = dot_product / (norm_query * norm_target)
                            cosine_distance = 1 - cosine_similarity
                            distances.append(cosine_distance)

                        # 构造返回格式
                        row_ret = {
                            "ids": [target_ids],
                            "metadatas": [target_metas],
                            "distances": [distances]
                        }

                        self.logger.debug("Calculated distances for %d target table rows", len(target_ids))
                    else:
                        # 如果获取详细信息失败，回退到向量召回
                        self.logger.warning("Failed to get target row details, falling back to vector recall")
                        large_k = min(1000, all_count)
                        row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
                            query_embeddings=[embedding],
                            n_results=large_k,
                            include=["metadatas", "distances"],
                        )
                else:
                    # 如果直接筛选没有找到结果，回退到向量召回
                    self.logger.debug("Direct filtering found no results, falling back to vector recall")
                    large_k = min(1000, all_count)
                    row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
                        query_embeddings=[embedding],
                        n_results=large_k,
                        include=["metadatas", "distances"],
                    )

            except Exception as e:
                self.logger.warning("Direct table filtering failed: %s, falling back to vector recall", e)
                # 回退到原来的向量召回方式
                large_k = min(1000, self._row_vs.collection.count())
                row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
                    query_embeddings=[embedding],
                    n_results=large_k,
                    include=["metadatas", "distances"],
                )

            if row_ret.get("ids"):
                # 筛选出包含目标表名的行
                filtered_results = []
                all_ids = row_ret["ids"][0]
                all_metas = row_ret["metadatas"][0]
                all_dists = row_ret["distances"][0]

                # 首先尝试只召回目标表本身的行
                target_table_results = []
                other_table_results = []

                for rid, meta, dist in zip(all_ids, all_metas, all_dists):
                    text = meta.get("text", "")
                    sheet_id = meta.get("sheet_id")

                    # 检查是否包含任何表名关键词
                    for keyword in table_keywords:
                        if keyword in text:
                            # 检查是否是目标表本身的行
                            table_name_pattern = f"表名:CBRC_EAST5_0_业务Mapping_V2_2_4_{keyword}"
                            if table_name_pattern in text:
                                # 这是目标表本身的行，大幅降低距离以提高排名
                                adjusted_dist = dist * 0.1  # 距离降低到原来的10%
                                # 避免对每条命中行都输出一行日志，改为后续汇总统计
                                # self.logger.debug("Target table row found (sheet_id=%s), distance adjusted: %.4f -> %.4f",
                                #                   sheet_id, dist, adjusted_dist)
                                target_table_results.append((rid, meta, adjusted_dist))
                            else:
                                # 这是其他表中提到目标表的行（如检核规则）
                                other_table_results.append((rid, meta, dist))
                            break

                # 优先使用目标表本身的行
                if target_table_results:
                    self.logger.debug("Found %d rows from target table itself, using only these rows", len(target_table_results))
                    filtered_results = target_table_results
                else:
                    self.logger.debug("No rows found from target table itself, using other matching rows")
                    filtered_results = other_table_results

                self.logger.debug("Table name filtering: %d total -> %d matched",
                                len(all_ids), len(filtered_results))

                if filtered_results:
                    # 按照调整后的距离对结果进行排序（距离越小越好）
                    filtered_results.sort(key=lambda x: x[2])  # x[2] 是 adjusted_dist

                    # 从排序后的结果中选择top_k_rows个
                    selected_results = filtered_results[:self.top_k_rows]

                    self.logger.debug("Top 5 filtered results after sorting:")
                    for i, (rid, meta, dist) in enumerate(selected_results[:5]):
                        sheet_id = meta.get("sheet_id")
                        rowid = meta.get("rowid")
                        text = meta.get("text", "")[:100]
                        self.logger.debug("  %d. sheet_id=%s, rowid=%s, dist=%.4f, \ntext=%s...",
                                        i+1, sheet_id, rowid, dist, text)
                        if "99991231" in meta.get("text", ""):
                            self.logger.debug("     🎯 Contains 99991231!")

                    # 重新构造返回格式
                    row_ret = {
                        "ids": [[r[0] for r in selected_results]],
                        "metadatas": [[r[1] for r in selected_results]],
                        "distances": [[r[2] for r in selected_results]]
                    }
                    self.logger.debug("Selected %d rows after table name matching", len(selected_results))
                else:
                    self.logger.warning("No rows matched the table name keywords, falling back to normal recall")
                    # 回退到正常的向量召回
                    row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
                        query_embeddings=[embedding],
                        n_results=self.top_k_rows,
                        include=["metadatas", "distances"],
                    )
            else:
                # 如果没有召回到任何结果，回退到正常召回
                row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
                    query_embeddings=[embedding],
                    n_results=self.top_k_rows,
                    include=["metadatas", "distances"],
                )
        else:
            # 没有提取到表名关键词，使用正常的向量召回
            self.logger.debug("No table keywords found, using normal vector recall")
            row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
                query_embeddings=[embedding],
                n_results=self.top_k_rows,
                include=["metadatas", "distances"],
            )
        # -------- 调试日志：原始 row 召回结果 -----------------------------
        try:
            _ids_log = row_ret.get("ids", [[]])[0][:5]  # type: ignore[index]
            _dists_log = row_ret.get("distances", [[]])[0][:5]  # type: ignore[index]
            self.logger.debug("Row recall raw | ids=%s distances=%s", _ids_log, _dists_log)
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log row recall raw failed: %s", _ex)

        if not row_ret.get("ids"):
            self.logger.info("未召回到任何行向量")
            return {}

        from typing import cast

        row_ids: List[str] = cast(List[str], row_ret["ids"][0])  # type: ignore[index]
        row_metas: List[Dict[str, Any]] = cast(List[Dict[str, Any]], row_ret["metadatas"][0])  # type: ignore[index]
        row_dists: List[float] = cast(List[float], row_ret["distances"][0])  # type: ignore[index]

        rows: List[Dict[str, Any]] = []
        for rid, meta, dist in zip(row_ids, row_metas, row_dists):
            # metadata 至少需包含 sheet_id, text
            rows.append(
                {
                    "id": rid,
                    "sheet_id": meta.get("sheet_id"),
                    "rowid": meta.get("rowid"),
                    "text": meta.get("text", ""),
                    "dist": float(dist),
                }
            )

        # ----------------------- 关键词相似度 (v2: difflib + 表名匹配增强) ----------------
        from difflib import SequenceMatcher
        import re

        # 预处理查询文本，移除常见分隔符
        q_norm = re.sub(r"[\s,:|]", "", question)

        # 关键词相似度计算中也使用table_keywords（已在前面提取）

        def _kw_sim(text: str) -> float:  # noqa: D401
            """使用 difflib 计算查询与文本开头的相似度，并增强表名匹配。"""
            if not text:
                return 0.0

            # 基础相似度计算
            text_prefix = text[: len(q_norm) + 20]  # 加一些 buffer
            text_prefix_norm = re.sub(r"[\s,:|]", "", text_prefix)

            if not text_prefix_norm:
                return 0.0

            # SequenceMatcher.ratio() is a good measure of similarity
            matcher = SequenceMatcher(a=q_norm, b=text_prefix_norm, autojunk=False)
            base_score = matcher.ratio()

            # 表名匹配增强：如果text中包含用户查询的表名关键词，给予额外加分
            table_boost = 0.0
            if table_keywords:
                for keyword in table_keywords:
                    if keyword in text:
                        # 根据关键词长度给予不同的加分，长关键词加分更多
                        boost = min(0.3, len(keyword) * 0.05)  # 最多加0.3分
                        table_boost = max(table_boost, boost)
                        # 每行都输出会造成大量重复日志，这里省略
                        # self.logger.debug("Table keyword '%s' found in text, boost: %.3f", keyword, boost)

            final_score = min(1.0, base_score + table_boost)  # 确保不超过1.0
            return final_score

        for r in rows:
            vec_score = 1.0 / (1.0 + r["dist"]) if r["dist"] is not None else 0.0
            kw_score = _kw_sim(r["text"])
            r["_score"] = (1 - self.kw_weight) * vec_score + self.kw_weight * kw_score
 
        # -------- 调试日志: 关键词 & 综合得分 -----------------------------
        try:
            self.logger.debug(
                "Normalized query for KW sim: %s", q_norm,
            )
            self.logger.debug(
                "Rows kw/vec/combined top10: %s",
                [
                    {
                        "rowid": r["rowid"],
                        "sheet": r["sheet_id"],
                        "vec_score": round(1 / (1 + r["dist"]), 4),
                        "kw": round(_kw_sim(r["text"]), 4),
                        "score": round(r["_score"], 4),
                    }
                    for r in rows[:10]
                ],
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log kw score failed: %s", _ex)

        # 3) sheet 向量召回，取最相关 sheet_id
        sheet_ret = self._sheet_vs.collection.query(  # type: ignore[attr-defined]
            query_embeddings=[embedding],
            n_results=1,
            include=["metadatas"],
        )

        top_sheet_id = None
        metas = sheet_ret.get("metadatas")  # type: ignore[index]
        # metas 结构: List[List[Dict]]，还需确保两层列表均非空
        if metas and metas[0]:
            top_sheet_id = metas[0][0].get("sheet_id")  # type: ignore[index]
            self.logger.debug("Top sheet_id via vector: %s", top_sheet_id)

        # 4) 行重排：优先同 sheet，其次按综合得分 (_score 越高越优)
        if top_sheet_id is not None:
            rows.sort(key=lambda r: (r["sheet_id"] != top_sheet_id, -r["_score"]))
        else:
            rows.sort(key=lambda r: -r["_score"])

        # -------- 调试日志：行重排后的前 10 条 -----------------------------
        try:
            self.logger.debug(
                "Rows after rerank (top10): %s",
                [
                    {
                        "sheet_id": r["sheet_id"],
                        "rowid": r["rowid"],
                        "dist": round(r["dist"], 4),
                    }
                    for r in rows[:10]
                ],
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log rows after rerank failed: %s", _ex)

        # 5) 组装返回 top_n 行文本
        selected_rows = rows[: self.top_n_rows]
        row_texts = [
            {"sheet_id": r["sheet_id"], "rowid": r["rowid"], "text": r["text"]}
            for r in selected_rows
            if r["text"]
        ]

        # -------- 调试日志：最终返回的行文本 -----------------------------
        try:
            # Log the complete row text without truncation for easier inspection.
            row_preview = [r.get("text", "") for r in row_texts]
            self.logger.debug(
                "Selected row_texts (top_n=%s): %s",
                self.top_n_rows,
                row_preview,
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log selected row_texts failed: %s", _ex)

        # ----------------- Sheet 范围控制 -----------------
        # 若上游已给出 sheet_ids（精准范围），则直接使用；否则才根据向量召回补充。
        existing_sids: list[str] = payload.get("sheet_ids", [])  # type: ignore[assignment]
        if existing_sids:
            sheet_ids = existing_sids
        else:
            sheet_ids = [f"sheet:{top_sheet_id}"] if top_sheet_id is not None else []

        self.logger.info(
            "Row recall finished | rows=%s top_sheet=%s", len(row_texts), top_sheet_id
        )
        return {
            "row_texts": row_texts,
            "sheet_ids": sheet_ids,
        } 