
import json
import logging
import re
from typing import Any, Dict, List, Tuple, Optional, Union

from sqlalchemy import create_engine, text
from src.orchestration.base_component import BaseComponent, register_component
from src.parser.llm.client import ask, LLMClientError
from pathlib import Path

logger = logging.getLogger(__name__)

# 创建一个模块级别的数据库引擎，以便复用连接
# 注意：这里的路径是硬编码的，如果未来数据库路径可配置，需要进行相应修改。
db_engine = create_engine("sqlite:///output/data.db")

def sanitize_for_table_name(name: str) -> str:
    """
    将文件名或sheet名进行清洗，使其成为一个合法的SQL表名。
    - 移除文件扩展名。
    - 将点、空格和其他特殊字符替换为下划线。
    """
    if '.' in name:
        name = name.rsplit('.', 1)[0]
    # 将任何非字母、数字或下划线的字符替换为下划线
    return re.sub(r'[^a-zA-Z0-9_]', '_', name)

@register_component
class DataItemIdentifier(BaseComponent):
    name = "data_item_identifier"
    """
    在识别出sheet后，进一步识别用户问题中提到的具体数据项。
    """


    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.prompt_template = self.config.get("prompt")
        self.provider = self.config.get("provider")
        self.model = self.config.get("model")
        if not self.prompt_template:
            raise ValueError("DataItemIdentifier的提示模板在配置中缺失。")

    def run(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行数据项识别逻辑。

        Args:
            state: 当前的流水线状态。必须包含 'question', 'file_name', 和 'sheet_name'。

        Returns:
            更新后的状态，包含 'identified_data_item' 和 'confidence'。
        """
        # ------------------------- 提取必要字段 -------------------------
        # 1) 直接从上游获取 "query" 字段作为用户问题
        question = state.get("query")

        # 2) sheet_name 直接取上游 SheetNameClassifier 的输出 selected_table
        sheet_name = state.get("selected_table")

        # 3) file_name: 若未显式提供，则尝试根据 sheet_id 反查
        file_name = state.get("file_name")

        if not file_name:
            sheet_ids = state.get("sheet_ids", [])
            first_sheet_id = None
            if sheet_ids:
                try:
                    # sheet_ids 形如 ["sheet:123"]
                    first_sheet_id = int(str(sheet_ids[0]).split(":", 1)[1])
                except (IndexError, ValueError):
                    first_sheet_id = None

            if first_sheet_id is not None:
                try:
                    with db_engine.connect() as conn:
                        res = conn.execute(
                            text(
                                """
SELECT fm.file_path
FROM sheet_metadata sm
JOIN file_metadata fm ON sm.file_id = fm.file_id
WHERE sm.sheet_id = :sid
LIMIT 1
"""
                            ),
                            {"sid": first_sheet_id},
                        ).fetchone()
                        if res and res[0]:
                            # 仅取文件名部分
                            file_name = Path(str(res[0])).name  # type: ignore[arg-type]
                except Exception as e:  # noqa: BLE001
                    logger.debug("根据 sheet_id 查询 file_name 失败: %s", e)

        # ------------------------- 校验 -------------------------
        if not question or not sheet_name or not file_name:
            logger.warning(
                "DataItemIdentifier: 缺少必要字段，已获取 question=%s, sheet_name=%s, file_name=%s。跳过此步骤。",
                bool(question), bool(sheet_name), bool(file_name),
            )
            return state

        try:
            # 1. 构建表名——与 ExcelParser 的生成逻辑保持一致，确保能够命中实际数据库表
            from src.ingestion.excel_parser import ExcelParser
            file_stem = Path(file_name).stem  # 去除文件扩展名
            table_name = ExcelParser._generate_table_name(file_stem, sheet_name)
            logger.info("尝试查询表: %s", table_name)

            # 2. 从数据库获取数据项名称
            data_items = self._get_data_items_from_db(table_name)
            if not data_items:
                logger.warning(f"在表 '{table_name}' 中未找到数据项。跳过识别。")
                return state

            # 3. 调用大模型识别数据项（可能返回一个或多个数据项）
            identified_raw, confidence = self._identify_item_with_llm(question, data_items)

            # 兼容旧实现：如果返回的是单个字符串，则包装成列表
            if isinstance(identified_raw, str) or identified_raw is None:
                identified_items: List[str] = [identified_raw] if identified_raw else []
            else:
                identified_items = identified_raw  # 已是 List[str]

            # 4. 更新状态（同时保留旧字段 identified_data_item 以免破坏兼容）
            if identified_items:
                state["identified_data_item"] = identified_items[0]
                # 新增：明确写入字段名，以便下游 MCP 使用
                state["field_name"] = identified_items[0]
            state["identified_data_items"] = identified_items
            state["confidence"] = confidence
            # 新增：将解析得到的数据库表名写入 state，供下游使用
            state["table_name"] = table_name
            # 回写供后续组件使用
            state["file_name"] = file_name
            state["sheet_name"] = sheet_name
            logger.info("识别出的数据项: %s，置信度: %s", identified_items, confidence)
            # 5. 若识别成功，则尝试直接从数据库获取完整行数据，供下游使用
            if identified_items:
                for _item in identified_items:
                    row_records = self.get_full_row_for_item(file_name, sheet_name, _item, return_all=True)
                    if not row_records:
                        continue

                    # 推断 sheet_id（若 state 中已有 sheet_ids 列表则取首个）
                    sheet_id_val = None
                    sheet_ids_in_state = state.get("sheet_ids", [])  # type: ignore[assignment]
                    if sheet_ids_in_state:
                        try:
                            sheet_id_val = int(str(sheet_ids_in_state[0]).split(":", 1)[1])
                        except Exception:  # noqa: BLE001
                            sheet_id_val = None

                    state.setdefault("row_texts", [])  # type: ignore[arg-type]

                    for row_record in row_records:
                        row_text = " | ".join([
                            f"{k}:{v}" for k, v in row_record.items() if v is not None
                        ])

                        # 去重：只在当前列表中不存在相同行文本时才添加
                        if not any(r.get("text") == row_text for r in state["row_texts"]):  # type: ignore[index]
                            state["row_texts"].insert(0, {  # type: ignore[index]
                                "sheet_id": sheet_id_val,
                                "rowid": row_record.get("rowid"),
                                "text": row_text,
                            })
                            logger.debug(
                                "[DataItemIdentifier] 已将目标行写入 state.row_texts，length=%s",  # noqa: D401
                                len(state["row_texts"]),
                            )

        except Exception as e:
            logger.error(f"DataItemIdentifier中发生未知错误: {e}")
            # 继续执行流水线

        return state

    def _get_data_items_from_db(self, table_name: str) -> List[str]:
        """
        从指定的表中获取“数据项名称”列表。
        """
        with db_engine.connect() as conn:
            # 首先，验证表是否存在
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name=:table_name"), {"table_name": table_name})
            if result.scalar_one_or_none() is None:
                logger.warning(f"表 '{table_name}' 在数据库中不存在。")
                return []

            try:
                # 根据需求，假设列名为“数据项名称”。
                query = text(f'SELECT "数据项名称" FROM "{table_name}"')
                result = conn.execute(query)
                items = [row[0] for row in result.fetchall() if row[0]]
                return items
            except Exception as e: # 更广泛地捕获可能的数据库错误
                logger.error(f"无法从表 '{table_name}' 中查询 '数据项名称': {e}")
                return []

    def _identify_item_with_llm(self, question: str, data_items: List[str]) -> Tuple[Union[List[str], str, None], float]:
        """
        使用大模型在问题中找到最可能的数据项。
        """
        # 构建受长度限制的 item_list 字符串，避免系统提示超长。
        MAX_ITEM_STR_LEN = 500 * 120  # 最大允许字符数

        item_parts: List[str] = []
        current_len = 0
        for itm in data_items:
            quoted = f"\"{itm}\""  # 统一加上引号，便于大模型识别
            # 若已存在元素，需要额外计算前缀", "的长度 2
            addition_len = len(quoted) + (2 if item_parts else 0)
            # 若加入当前元素会超过长度上限，则停止添加
            if current_len + addition_len > MAX_ITEM_STR_LEN:
                break
            item_parts.append(quoted)
            current_len += addition_len

        item_list_str = ", ".join(item_parts)

        system_prompt = (
            self.prompt_template.replace("{item_list}", item_list_str)
            .replace("{question}", question or "")
        )

        try:
            response = ask(
                f"用户的问题是: \"{question}\"",
                system=system_prompt,
                provider=self.provider,
                model=self.model,
                temperature=0,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response)
            # 兼容两种返回格式：{"data_item": "xx"} 或 {"data_items": [..]}
            items_field = None
            if "data_items" in result:
                items_field = result.get("data_items")
            else:
                items_field = result.get("data_item")

            # 兼容多种置信度格式：
            # - 数值: 0.87
            # - 字符串: "0.87"
            # - 列表: [0.87, 0.82]
            conf_raw = result.get("confidence", 0.0)

            if isinstance(conf_raw, list):
                # 如果是列表，则取最大值作为整体置信度
                try:
                    confidence = float(max(conf_raw)) if conf_raw else 0.0
                except (TypeError, ValueError):
                    confidence = 0.0
            else:
                try:
                    confidence = float(conf_raw)
                except (TypeError, ValueError):
                    confidence = 0.0

            # 统一转成 List[str]
            if isinstance(items_field, str):
                identified_items: List[str] = [items_field]
            elif isinstance(items_field, list):
                identified_items = [item for item in items_field if isinstance(item, str)]
            else:
                identified_items = []

            # 过滤：仅保留在 data_items 列表中的项
            identified_items = [itm for itm in identified_items if itm in data_items]

            if identified_items:
                return identified_items, confidence
            else:
                if items_field:
                    logger.warning("大模型识别出的数据项 %s 均不在提供的数据项列表中，已忽略。", items_field)
                return None, 0.0

        except (json.JSONDecodeError, TypeError, ValueError) as e:
            logger.error(f"解析大模型返回的数据项识别结果失败: {e}")
            return None, 0.0
        except LLMClientError as e:
            logger.error(f"调用大模型进行数据项识别时出错: {e}")
            return None, 0.0
        except Exception as e:
            logger.error(f"处理数据项识别时发生未知错误: {e}")
            return None, 0.0

    def get_full_row_for_item(
        self,
        file_name: str,
        sheet_name: str,
        data_item: str,
        *,
        return_all: bool = False,
    ) -> Union[Optional[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        根据给定的数据项，从其所在的表中获取完整的行数据。

        Args:
            file_name: Excel 文件名。
            sheet_name: 工作表名称。
            data_item: 目标数据项。
            return_all: 若为 ``True``，则返回 **所有** 命中的行列表；否则只返回首行。

        Returns:
            - 当 ``return_all`` 为 ``False`` 时，返回 ``dict`` 或 ``None``；
            - 当 ``return_all`` 为 ``True`` 时，返回 ``List[dict]``，若无记录则为空列表。
        """
        logger.debug(
            "[DataItemIdentifier] get_full_row_for_item 调用，file_name=%s, sheet_name=%s, data_item=%s",  # noqa: D401
            file_name,
            sheet_name,
            data_item,
        )
        # 统一使用 ExcelParser 的生成逻辑
        from src.ingestion.excel_parser import ExcelParser
        if file_name:
            file_stem = Path(file_name).stem
            table_name = ExcelParser._generate_table_name(file_stem, sheet_name)
            logger.debug("[DataItemIdentifier] 解析得到 table_name=%s", table_name)
        else:
            # 若 file_name 缺失，尝试模糊匹配“_*_{sheet}” 的表
            sanitized_sheet = ExcelParser._generate_table_name("dummy", sheet_name).split("_", 1)[1]
            with db_engine.connect() as conn:
                res = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE :pat LIMIT 1"), {"pat": f"%_{sanitized_sheet}"}).fetchone()
                if not res:
                    return None
                table_name = res[0]
            logger.debug("[DataItemIdentifier] 根据 sheet 模糊匹配得到表名: %s", table_name)
        
        with db_engine.connect() as conn:
            try:
                logger.debug("[DataItemIdentifier] 正在查询表 %s 中的数据项 '%s'", table_name, data_item)
                query = text(f'SELECT * FROM "{table_name}" WHERE "数据项名称" = :data_item')
                result = conn.execute(query, {"data_item": data_item})

                rows = result.mappings().all()

                # 若未命中，尝试使用 TRIM 及 LIKE 做宽松匹配
                if not rows:
                    logger.debug("[DataItemIdentifier] 精确匹配未命中，尝试 LIKE 宽松匹配…")
                    like_query = text(f'SELECT * FROM "{table_name}" WHERE TRIM("数据项名称") LIKE :pat')
                    result = conn.execute(like_query, {"pat": f"%{data_item.strip()}%"})
                    rows = result.mappings().all()
                else:
                    logger.debug("[DataItemIdentifier] 精确匹配成功，获取 %s 行。", len(rows))

                if return_all:
                    return [dict(r) for r in rows]

                # 仅返回首行
                if rows:
                    return dict(rows[0])
                return None
            except Exception as e:
                logger.error(f"从表 '{table_name}' 中为数据项 '{data_item}' 获取完整行数据失败: {e}")
                return [] if return_all else None

