from __future__ import annotations
from typing import Dict, Any, List
import json
import logging
import re

from ..base_component import BaseComponent, register_component
from src.parser.llm.client import ask, LLMClientError


"""QueryExpanderComponent

调用大模型 (Ollama / Qwen3) 对用户问题进行关键词拆分与语义扩展。
输出 `keywords` 列表供下游 BM25 召回使用。

配置示例 (qa_pipeline.yaml)::

  - name: query_expander
    config:
      provider: ollama
      model: qwen3:latest
      think: false
"""

@register_component
class QueryExpanderComponent(BaseComponent):  # noqa: D101
    name = "query_expander"

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # 保存 LLM 调用相关配置；提供合理默认值
        self._provider: str | None = config.get("provider", "ollama")
        self._model: str | None = config.get("model", "qwen3:latest")
        self._think: bool | None = config.get("think", False)
        # 最多返回多少关键词，避免 prompt 污染
        self.max_kw: int = int(config.get("max_keywords", 12))

        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def _fallback_split(self, q: str) -> List[str]:  # noqa: D401
        """简易回退：抓取连续 2+ 中文/英文字母/数字串。"""
        toks = re.findall(r"[\u4e00-\u9fa5A-Za-z0-9]{2,}", q)
        # 去重并保序
        seen: set[str] = set()
        uniq = [t for t in toks if not (t in seen or seen.add(t))]
        return uniq[: self.max_kw]

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        q: str = payload.get("query", "").strip()
        if not q:
            return {}

        sys_prompt = (
            "你是中文检索助手，任务是将用户问题拆解为若干关键词或短语，并进行必要的同义/近义扩展。\n"
            "输出 JSON 数组，不要解释，例如：{\"keywords\": [\"自营资金\", \"余额表\"]}。\n"
            "每个关键词 2-6 字，最多 12 个。"
        )
        user_prompt = f"用户问题：{q}"

        keywords: List[str] = []
        try:
            resp = ask(
                user_prompt,
                system=sys_prompt,
                provider=self._provider,
                model=self._model,
                think=self._think,
                temperature=0,
                response_format={"type": "json_array"},
            )
            # ask() 可能已自动提取 json，但仍做二次解析保证安全
            resp_json = json.loads(resp)
            if isinstance(resp_json, list):
                keywords = [str(t).strip() for t in resp_json if str(t).strip()]  # type: ignore[arg-type]
            elif isinstance(resp_json, dict):
                # 常见返回格式 {"keywords": [...]} 或 {"关键词": [...]}
                for key in ("keywords", "关键词", "keys", "key_words"):
                    val = resp_json.get(key)
                    if isinstance(val, list):
                        keywords = [str(t).strip() for t in val if str(t).strip()]
                        break
                # 若未找到, 考虑 dict 本身 kv 值均为 keyword->score 等
                if not keywords:
                    keywords = [str(k).strip() for k in resp_json.keys()][: self.max_kw]
        except (LLMClientError, json.JSONDecodeError) as ex:
            self.logger.warning("LLM 关键词扩展失败，回退本地切词: %s", ex)
            keywords = self._fallback_split(q)
        except Exception as ex:  # noqa: BLE001
            self.logger.error("QueryExpander 异常: %s", ex)
            keywords = self._fallback_split(q)

        # 截断到 max_kw
        keywords = keywords[: self.max_kw]

        self.logger.debug("keywords=%s", keywords)
        return {"keywords": keywords} 