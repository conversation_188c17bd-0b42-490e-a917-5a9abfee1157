"""Base classes for pipeline components."""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict, List

from .registry import ComponentRegistry

__all__ = [
    "BaseComponent",
    "register_component",
]


class BaseComponent(ABC):  # noqa: D101
    """所有可插拔组件的抽象基类。"""

    # 每个组件都必须声明唯一的 name，用于注册与查找
    name: str = "base"

    # 可选：声明前置输入/输出字段，便于校验与可视化
    inputs: List[str] = []
    outputs: List[str] = []

    def __init__(self, config: Dict[str, Any] | None = None) -> None:  # noqa: D401
        self.config: Dict[str, Any] = config or {}

        # ------------------------------------------------------------------
        # 组件级启用开关：当 config 中显式声明 enabled=False 时，
        # 该组件在运行阶段直接跳过，返回空字典。
        # ------------------------------------------------------------------
        self._enable_toggle_wrapper()

        # 供子类在 setup 中完成模型加载、连接初始化等重量级操作
        self.setup(self.config)

        # ------------------------------------------------------------------
        # 通用 Mock 支持：
        # 若组件配置中存在以下任意一种形式，则在运行时直接返回预设结果，
        #   而不会真正执行重量级的大模型 / 数据库调用。
        # 1) mock_output: { ... }                 # 简写，始终启用
        # 2) mock: {enabled: true, output: { ... }}  # 详细写法，可显式开关
        # 3) mock: true  + mock_output: { ... }   # 拆分写法
        # 这样在单元测试 / 集成测试中，可通过修改 YAML 即可 Mock
        # ------------------------------------------------------------------
        self._enable_mock_wrapper()

    # ------------------------------------------------------------------
    # Lifecycle hooks
    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        """子类可覆写以执行初始化逻辑。"""

    @abstractmethod
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401,E501
        """核心执行逻辑，读取 `payload` 并返回增量字典。"""

    def apply_io_config(self, inputs: List[str] | None = None, outputs: List[str] | None = None) -> None:  # noqa: D401
        """由 Pipeline 注入 YAML 中声明的 inputs/outputs，并进行一致性校验。"""
        self._yaml_inputs: List[str] = list(inputs) if inputs else []
        self._yaml_outputs: List[str] = list(outputs) if outputs else []
        self._validate_io()

    # ------------------------------------------------------------------
    # 内部工具：检查并注入 Mock 包装器
    # ------------------------------------------------------------------
    def _enable_mock_wrapper(self) -> None:  # noqa: D401
        """在实例级别为 ``run`` 方法注入 Mock 逻辑。"""

        # 若组件类没有覆盖 run（理论上不会发生，因为 BaseComponent.run 是抽象方法）
        if "run" not in self.__class__.__dict__:
            return  # nothing to wrap

        import types  # 局部导入避免循环

        original_run = self.run  # 已绑定方法，可直接保存

        def _wrapped_run(this, payload: Dict[str, Any], **kwargs: Any):  # noqa: D401
            mock_ret = this._get_mock_output()
            if mock_ret is not None:
                # 若组件实现了 _process_mock_output，则允许其自定义处理逻辑
                if hasattr(this, "_process_mock_output") and callable(getattr(this, "_process_mock_output")):
                    try:
                        return this._process_mock_output(mock_ret, payload)  # type: ignore[attr-defined]
                    except Exception as _ex:  # noqa: BLE001
                        # 若自定义处理失败，退回原始 mock 输出，避免中断流程
                        import logging
                        logging.getLogger(__name__).warning(
                            "_process_mock_output 处理失败，已回退原始 mock 输出: %s", _ex,
                        )
                        return mock_ret
                return mock_ret
            # 否则调用原始实现
            return original_run(payload, **kwargs)

        # 绑定到实例，避免影响类级别其他实例
        self.run = types.MethodType(_wrapped_run, self)  # type: ignore[assignment]

    def _enable_toggle_wrapper(self) -> None:  # noqa: D401
        """若配置声明 enabled=False，则包装 run 方法为 no-op。"""

        # config 中未设置或为 True → 正常执行
        if self.config.get("enabled", True):
            return

        import types  # 局部导入避免循环
        import logging

        def _noop_run(this, payload: Dict[str, Any], **kwargs: Any):  # noqa: D401
            logging.getLogger(__name__).info("⏩ 组件 %s 已被 disabled，直接跳过。", getattr(this, "name", this.__class__.__name__))
            return {}

        # 仅在类直接实现了 run 时才替换，避免重复包裹
        if "run" in self.__class__.__dict__:
            self.run = types.MethodType(_noop_run, self)  # type: ignore[assignment]

    # ------------------------------------------------------------------
    # 修复 Mock 解析逻辑：
    # 1. 若显式声明 mock=False 或 mock: {enabled: false} → 强制关闭 Mock。
    # 2. 其余情况下保持向后兼容，支持三种开启方式：
    #    a) mock_output: {...}
    #    b) mock: true + mock_output: {...}
    #    c) mock: {enabled: true, output: {...}}
    # ------------------------------------------------------------------
    def _get_mock_output(self) -> "Dict[str, Any] | None":  # noqa: D401
        """解析配置并返回 Mock 输出，若未启用则返回 None。"""

        cfg = self.config or {}

        # ------- 优先级 1：显式 mock=false 关闭 Mock -------
        mock_flag = cfg.get("mock", None)
        if mock_flag is False:
            return None

        # ------- 优先级 2：mock 为 dict，读取 enabled & output -------
        if isinstance(mock_flag, dict):
            if not mock_flag.get("enabled", True):
                # 显式 disabled
                return None
            # enabled 为 True → 读取 output；如缺失则回落到顶层 mock_output
            output = mock_flag.get("output")
            if output is None:
                output = cfg.get("mock_output")
            return output if isinstance(output, dict) else None  # type: ignore[return-value]

        # ------- 优先级 3：mock 为 True（拆分写法） -------
        if mock_flag is True:
            return cfg.get("mock_output")  # type: ignore[return-value]

        # ------- 优先级 4：不存在 mock 字段，仅有 mock_output -------
        if isinstance(cfg.get("mock_output"), dict):
            return cfg.get("mock_output")  # type: ignore[return-value]

        # 默认关闭
        return None

    # ------------------------------------------------------------------
    def _validate_io(self) -> None:  # noqa: D401
        """校验代码声明与 YAML 配置的 inputs/outputs 是否一致。

        允许以下情况通过：
        1. 代码与 YAML 均未声明。
        2. 仅 YAML 声明 → 以 YAML 为准，覆盖实例属性。
        3. 仅代码声明 → 保持不变（向后兼容）。
        若二者均声明但不一致，则抛出 ValueError。"""
        # 将枚举 Field 转为 str，确保可比
        def _to_str_list(objs: List[Any]) -> List[str]:  # noqa: D401
            return [str(o) for o in objs]

        code_inputs = _to_str_list(getattr(self, "inputs", []))
        code_outputs = _to_str_list(getattr(self, "outputs", []))
        yaml_inputs = _to_str_list(getattr(self, "_yaml_inputs", []))
        yaml_outputs = _to_str_list(getattr(self, "_yaml_outputs", []))

        # ---------------- inputs ----------------
        if yaml_inputs and code_inputs:
            if set(yaml_inputs) != set(code_inputs):
                raise ValueError(
                    f"组件 {self.name} 的 inputs 与配置文件不一致: YAML={yaml_inputs} Code={code_inputs}")
        elif yaml_inputs and not code_inputs:
            # 仅 YAML 声明，覆盖到实例属性
            self.inputs = yaml_inputs  # type: ignore[assignment]
        # 其它情况保持不变

        # ---------------- outputs ---------------
        if yaml_outputs and code_outputs:
            if set(yaml_outputs) != set(code_outputs):
                raise ValueError(
                    f"组件 {self.name} 的 outputs 与配置文件不一致: YAML={yaml_outputs} Code={code_outputs}")
        elif yaml_outputs and not code_outputs:
            self.outputs = yaml_outputs  # type: ignore[assignment]
        # else 保持不变


# ----------------------------------------------------------------------
# Helper decorator for easy registration
# ----------------------------------------------------------------------

def register_component(cls: type[BaseComponent]) -> type[BaseComponent]:  # noqa: D401
    """类装饰器：自动将组件注册到 ComponentRegistry。"""

    if not hasattr(cls, "name") or cls.name == "base":  # noqa: WPS421
        raise ValueError("组件必须定义唯一的 `name` 字段才能注册")

    ComponentRegistry.register(cls)
    return cls 