from __future__ import annotations
from enum import Enum, unique

"""全局字段注册表。

此枚举用于统一管理流水线各组件之间传递的 `payload` 字段名称，
确保命名唯一且语义清晰，避免随意拼写导致的冲突与歧义。

后续新增字段时，请先在此处注册，再在组件中引用 ``Field.xxx``。
"""

@unique
class Field(str, Enum):
    """所有可用字段枚举。"""

    # 用户输入 & 基础
    query = "query"                      # 原始用户问题
    intent = "intent"                    # 查询意图标签
    keywords = "keywords"                # 关键词扩展

    # 回答生成相关
    answer = "answer"                    # 最终答案文本

    # 召回 / 检索
    query_vector = "query_vector"        # 查询向量
    row_texts = "row_texts"              # 候选行文本列表
    tables_preview = "tables_preview"    # 表头 / 预览信息
    sheet_meta = "sheet_meta"            # sheet 元数据
    column_texts = "column_texts"        # 列文本
    file_texts = "file_texts"            # 文件级全文
    sheet_ids = "sheet_ids"              # 检索得到的 sheet id 列表
    retrieved_ids = "retrieved_ids"      # 检索得到的行 id 列表

    # SQL 相关
    sql = "sql"                          # 生成的 SQL 语句
    sql_result = "sql_result"            # SQL 执行结果（markdown / dataframe）

    # 表结构
    tables = "tables"                    # 数据库业务表列表
    selected_table = "selected_table"    # 用户当前关注的表
    table_schemas = "table_schemas"      # 表结构信息

    # 意图细分 / 策略
    strategy = "strategy"
    strategy_params = "strategy_params"
    intent_result = "intent_result"
    level_probs = "level_probs"

    # 其它
    # ... 如有新增字段，请补充到此处 ...

    def __str__(self) -> str:  # noqa: D401
        # 允许直接 ``str(Field.query)`` 得到 "query"
        return self.value 