from __future__ import annotations
# type: ignore[override]
from typing import Optional
import logging
import os

from ..parser.llm.client import ask, LLMClientError
from ..utils.config import get_task_cfg

"""LLM 驱动的答案摘要。

该模块封装了一个 `summarize_answer` 函数，调用已有的 `parser.llm.client.ask`，
根据用户问题与候选上下文，让大模型生成更加精炼、结构化的中文回答。
"""


logger = logging.getLogger(__name__)


# ----------------------------------------------------------------------
# Public API
# ----------------------------------------------------------------------

_TASK_CFG = get_task_cfg("qa_answer")


# 新增 provider 参数，允许上层显式指定调用的大模型服务
def summarize_answer(
    question: str,
    context: str,
    *,
    max_words: int | None = None,
    model: Optional[str] = None,
    provider: Optional[str] = None,
) -> str:  # noqa: D401
    """调用大模型生成简洁回答。

    Parameters
    ----------
    question : str
        用户自然语言问题。
    context : str
        经检索得到的候选上下文（行文本、表摘要等）。
    max_words : int, optional
        允许回答的最大中文字符数，默认 300。
    model : str | None, optional
        指定模型名称，None 时使用环境变量或默认。
    """
    # 配置优先级：函数参数 > YAML 配置 > 环境变量 / 默认
    cfg = _TASK_CFG

    if max_words is None:
        max_words = cfg.get("max_words", 300)

    if model is None:
        model = cfg.get("model") or os.getenv("LLM_MODEL", "qwen3:latest")

    # provider 优先级：函数参数 > YAML 配置 > 环境变量 / 默认
    if provider is None:
        provider = cfg.get("provider")

    system_msg = cfg.get(
        "system_prompt",
        "你是一位中文数据分析助手，能够精准、简洁地回答用户问题。若上下文不足以回答，应如实说明。",
    )
    prompt = (
        f"请参考以下信息，用中文回答，不超过 {max_words} 字。\n\n"
        f"候选上下文：\n{context[:120*512]}\n\n"  # 避免超长 prompt
        f"候选信息中给出的是数据项逐行的属性信息，请回答用户问题：\n\n{question}\n\n"
    )

    try:
        answer = ask(
            prompt,
            system=system_msg,
            model=model,
            provider=provider,
            think=cfg.get("think"),
        )  # type: ignore[arg-type]

        # 若模型返回包含 <think>...</think> 或 <think> 段落，予以去除
        import re

        def _strip_think(text: str) -> str:  # noqa: D401
            # 删除 <think> 块（单行或多行）
            text = re.sub(r"<think>[\s\S]*?</think>", "", text, flags=re.IGNORECASE)
            # 删除以 <think> 开头到行尾的内容（无闭标签情况）
            text = re.sub(r"<think>.*", "", text, flags=re.IGNORECASE)
            return text.strip()

        clean = _strip_think(answer)

        return clean
    except LLMClientError as ex:  # noqa: BLE001
        logger.warning("LLM summarization failed: %s", ex)
        return "" 