"""Pipeline 构建与执行逻辑，支持 Fixed + Dynamic 组件。"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List, Any, Callable

import logging
import yaml  # type: ignore
import importlib
import pkgutil
import sys
import time

from .registry import ComponentRegistry

# 模块级 logger，方便统一调试输出
logger = logging.getLogger(__name__)

# ------------------------------------------------------------------
# Auto-load components package to ensure registration when running CLI
# ------------------------------------------------------------------


def _autoload_components() -> None:  # noqa: D401
    """自动加载组件包，支持多个包路径。"""

    # 支持的包列表，可按需扩展
    packages = [
        "src.orchestration.components",  # 旧版路径
        "src.qa_pro.components",        # 新版路径
    ]

    for package_name in packages:
        try:
            pkg = importlib.import_module(package_name)
        except ModuleNotFoundError:
            # 包不存在时跳过，不抛异常以兼容不同部署场景
            continue

        pkg_path = Path(pkg.__file__).parent  # type: ignore[attr-defined]
        for module in pkgutil.iter_modules([str(pkg_path)]):
            full_name = f"{package_name}.{module.name}"
            if full_name not in sys.modules:
                importlib.import_module(full_name)

# ----------------------------------------------------------------------
# Helper
# ----------------------------------------------------------------------


def _load_yaml(path: str | Path) -> Dict[str, Any]:  # noqa: D401
    """轻量 YAML 加载。不存在返回空 dict。"""
    p = Path(path)
    if not p.exists():
        return {}
    with p.open("r", encoding="utf-8") as f:
        return yaml.safe_load(f) or {}


# ----------------------------------------------------------------------
# MixedPipeline
# ----------------------------------------------------------------------


class MixedPipeline:  # noqa: D101
    def __init__(self, cfg_path: str | Path = "config/pipeline.yaml") -> None:  # noqa: D401
        self._cfg_path = Path(cfg_path)
        self._cfg: Dict[str, Any] = _load_yaml(self._cfg_path)
        self.fixed_cfg: List[Dict[str, Any]] = self._cfg.get("fixed_steps", [])
        self.candidates: List[Dict[str, Any]] = self._cfg.get("dynamic_candidates", [])
        self.planner_cfg: Dict[str, Any] = self._cfg.get("planner", {})
        self.components: List[Any] = []
        # 保存与 components 顺序对应的配置字典，便于在 execute 阶段解析 when/skip_if 条件
        self.comp_cfgs: List[Dict[str, Any]] = []

    # ------------------------------------------------------------------
    # Build & Execute
    # ------------------------------------------------------------------
    def build(self, user_query: str) -> None:  # noqa: D401
        """根据配置 + LLMPlanner 构建组件实例列表。"""
        _autoload_components()
        # 重置组件与配置列表（支持重复 build 调用）
        self.comp_cfgs = []
        # 1) build fixed parts -----------------------------------------
        fixed_parts: List[Any] = []
        for conf in self.fixed_cfg:
            # 若显式声明 enabled=False，则跳过构建
            if conf.get("enabled") is False or conf.get("config", {}).get("enabled") is False:
                logger.info("🛑 Fixed 组件 %s 已被禁用，构建阶段跳过。", conf.get("name"))
                continue

            comp_cls = ComponentRegistry.get(conf["name"])
            comp = comp_cls(conf.get("config", {}))  # type: ignore[arg-type]
            comp.name = conf["name"]  # Inject name for runtime identification
            # 注入 inputs/outputs 元数据
            if hasattr(comp, "apply_io_config"):
                comp.apply_io_config(conf.get("inputs"), conf.get("outputs"))  # type: ignore[arg-type]
            fixed_parts.append(comp)
            # 记录配置，用于运行时条件判断
            self.comp_cfgs.append(conf)

        # 2) dynamic plan via LLMPlanner --------------------------------
        dynamic_plan: List[Dict[str, Any]] = []
        try:
            from .llm_planner import LLMPlanner  # type: ignore  # noqa: WPS433 (intra-package import)

            dynamic_plan = LLMPlanner(self.candidates, **self.planner_cfg).plan(user_query)
        except ModuleNotFoundError:
            # Planner 未实现时采用空动态流程
            dynamic_plan = []
        except Exception as ex:  # noqa: BLE001
            # Planner 失败时可降级
            import logging
            logging.warning(f"[MixedPipeline] Planner 失败，已降级，异常信息: {ex}")
            dynamic_plan = []

        # 3) ensure required components
        required_names = {c["name"] for c in self.candidates if c.get("required")}
        present_names = {step["name"] for step in dynamic_plan}
        missing_required = required_names - present_names
        for name in missing_required:
            dynamic_plan.append({"name": name})

        # 4) Reorder dynamic_plan to follow the order defined in `self.candidates`
        ordered_dynamic_plan: List[Dict[str, Any]] = []
        seen: set[str] = set()

        # first, keep candidates order
        for cand in self.candidates:
            name = cand["name"]
            for step in dynamic_plan:
                if step["name"] == name and name not in seen:
                    ordered_dynamic_plan.append(step)
                    seen.add(name)

        # then, append any additional steps not in candidates (edge cases)
        for step in dynamic_plan:
            if step["name"] not in seen:
                ordered_dynamic_plan.append(step)
                seen.add(step["name"])

        # 5) build dynamic component instances
        dynamic_parts: List[Any] = []
        for conf in ordered_dynamic_plan:
            # 若显式声明 enabled=False，则跳过构建
            if conf.get("enabled") is False or conf.get("config", {}).get("enabled") is False:
                logger.info("🛑 Dynamic 组件 %s 已被禁用，构建阶段跳过。", conf.get("name"))
                continue

            comp_cls = ComponentRegistry.get(conf["name"])
            comp = comp_cls(conf.get("config", {}))  # type: ignore[arg-type]
            comp.name = conf["name"]  # Inject name for runtime identification
            if hasattr(comp, "apply_io_config"):
                comp.apply_io_config(conf.get("inputs"), conf.get("outputs"))  # type: ignore[arg-type]
            dynamic_parts.append(comp)
            self.comp_cfgs.append(conf)

        self.components = fixed_parts + dynamic_parts

    # ------------------------------------------------------------------
    # 条件表达式解析
    # ------------------------------------------------------------------
    @staticmethod
    def _eval_expr(expr: str, state: Dict[str, Any]) -> bool:  # noqa: D401
        """安全地执行表达式，返回 bool 结果。

        仅开放常见内置函数，避免执行任意代码。表达式中可通过 ``state`` 访问流水线上下文。
        """
        # 允许在条件表达式中调用的内置函数集合
        # 补充了 bool 以支持在 YAML 中常见的 ``bool(state.get(...))`` 写法。
        allowed_builtins = {
            "len": len,
            "any": any,
            "all": all,
            "min": min,
            "max": max,
            "bool": bool,
        }
        try:
            return bool(eval(expr, {"__builtins__": allowed_builtins}, {"state": state}))
        except Exception as ex:  # noqa: BLE001
            logger.warning("[ConditionEval] 解析表达式 %s 失败: %s，默认为 False", expr, ex)
            return False

    # ------------------------------------------------------------------
    def execute(
        self,
        payload: Dict[str, Any],
        *,
        progress_cb: "Callable[[str, float, Dict[str, Any]], None] | None" = None,
    ) -> Dict[str, Any]:  # noqa: D401
        if not self.components:
            raise RuntimeError("Pipeline 未构建，请先调用 build()")
        
        data = dict(payload)
        timings: list[dict[str, float | str]] = []

        for idx, comp in enumerate(self.components):
            # --------------------------------------------------------------
            # 1) 解析 when / skip_if 条件
            # --------------------------------------------------------------
            comp_cfg = self.comp_cfgs[idx] if idx < len(self.comp_cfgs) else {}
            # skip_if 优先级更高
            if comp_cfg.get("skip_if") and self._eval_expr(comp_cfg["skip_if"], data):
                logger.info("⏩ 跳过组件 %s，满足 skip_if 条件。", getattr(comp, "name", comp.__class__.__name__))
                continue
            if comp_cfg.get("when") and not self._eval_expr(comp_cfg["when"], data):
                logger.info("⏩ 跳过组件 %s，未满足 when 条件。", getattr(comp, "name", comp.__class__.__name__))
                continue

            # --------------------------------------------------------------
            # 2) 正常执行组件
            # --------------------------------------------------------------
            logger.debug(
                "[Loop] 即将执行组件 %s，当前 row_texts len=%s",  # noqa: D401
                getattr(comp, "name", comp.__class__.__name__),
                len(data.get("row_texts", [])) if isinstance(data.get("row_texts"), list) else 0,
            )
            if "answer" in data:
                break

            # 进度回调：组件开始
            if progress_cb is not None:
                try:
                    progress_cb(getattr(comp, "name", comp.__class__.__name__), None, data)
                except Exception as _ex:  # noqa: BLE001
                    logger.debug("progress_cb error: %s", _ex)

            start_ts = time.monotonic()
            data.update(comp.run(data))
            elapsed = time.monotonic() - start_ts
            timings.append({"name": getattr(comp, "name", comp.__class__.__name__), "duration": round(elapsed, 3)})
            # 记录耗时
            logger.debug(
                "[Loop] 组件 %s 执行完毕，用时 %.2fs，row_texts len=%s",  # noqa: D401
                getattr(comp, "name", comp.__class__.__name__),
                elapsed,
                len(data.get("row_texts", [])) if isinstance(data.get("row_texts"), list) else 0,
            )

            # 进度回调
            if progress_cb is not None:
                try:
                    progress_cb(getattr(comp, "name", comp.__class__.__name__), elapsed, data)
                except Exception as _ex:  # noqa: BLE001
                    logger.debug("progress_cb error: %s", _ex)

            # 如果某个组件生成了答案，则为标准退出条件
            if "answer" in data:
                break
        data["_timings"] = timings
        return data 