from __future__ import annotations

"""
DBWriter 负责将 ParsedTable 写入 SQLite 数据库，并维护元数据表结构。
具体 schema 定义与约束参见 docs/excel_ingestion_design.md。
"""

from pathlib import Path
from typing import Any, List, Optional, Dict
from array import array

import pandas as pd  # type: ignore
from sqlalchemy import Engine, create_engine, inspect, text

from ..ingestion.excel_parser import ParsedTable


class DBWriter:  # noqa: D101
    def __init__(self, db_path: Path | str = "output/data.db") -> None:  # noqa: D401
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.engine: Engine = create_engine(f"sqlite:///{self.db_path}")
        self._ensure_metadata_tables()

    # --- Public API -----------------------------------------------------
    def write_table(self, parsed: ParsedTable, *, if_exists: str = "append") -> str:  # noqa: D401
        """将 DataFrame 写入 SQLite。

        Parameters
        ----------
        parsed : ParsedTable
            待写入的表结构与数据。
        if_exists : {"fail", "replace", "append"}
            控制已存在表名时的行为。
        """
        # ---- 过滤全空行 --------------------------------------------------
        clean_df = (
            parsed.dataframe.replace(r"^\s*$", pd.NA, regex=True)  # 将空字符串视作 NA
            .dropna(how="all")  # 移除整行均为空值的行
        )

        if clean_df.empty:
            # 若过滤后无有效数据，仅确保表结构存在（如需）。
            if not self.table_exists(parsed.name):
                # 使用原列定义创建空表结构
                empty_df = parsed.dataframe.head(0)
                empty_df.to_sql(parsed.name, self.engine, if_exists="fail", index=False)  # type: ignore[arg-type]
            return parsed.name

        # ---- 动态新增新列（基于 clean_df ）-----------------------------
        if if_exists == "append" and self.table_exists(parsed.name):
            existing_cols = set(pd.read_sql(f"SELECT * FROM {parsed.name} LIMIT 0", self.engine).columns)
            new_cols = [c for c in clean_df.columns if c not in existing_cols]
            for col in new_cols:
                with self.engine.begin() as conn:
                    conn.execute(text(f'ALTER TABLE "{parsed.name}" ADD COLUMN "{col}"'))

        # pandas stub 将 "if_exists" 标注为 Literal；此处动态传入合法值，忽略类型检查。
        clean_df.to_sql(parsed.name, self.engine, if_exists=if_exists, index=False)  # type: ignore[arg-type]

        return parsed.name

    # --- Internal helpers ----------------------------------------------
    def _ensure_metadata_tables(self) -> None:  # noqa: D401
        """创建 file_metadata & sheet_metadata 等表（若不存在）。"""
        with self.engine.begin() as conn:
            conn.execute(
                text(
                    """
CREATE TABLE IF NOT EXISTS file_metadata (
  file_id INTEGER PRIMARY KEY AUTOINCREMENT,
  file_path TEXT UNIQUE,
  status TEXT DEFAULT 'pending'
);
"""
                )
            )
            
            conn.execute(
                text(
                    """
CREATE TABLE IF NOT EXISTS file_attributes (
  file_id INTEGER REFERENCES file_metadata(file_id),
  key TEXT,
  value TEXT,
  embedding_text TEXT,
  vector BLOB,
  PRIMARY KEY (file_id, key)
);
"""
                )
            )
            
            conn.execute(
                text(
                    """
CREATE TABLE IF NOT EXISTS sheet_metadata (
  sheet_id INTEGER PRIMARY KEY AUTOINCREMENT,
  file_id INTEGER REFERENCES file_metadata(file_id),
  sheet_name TEXT,
  table_name TEXT,
  status TEXT DEFAULT 'pending',
  UNIQUE(file_id, sheet_name)
);
"""
                )
            )
            
            conn.execute(
                text(
                    """
CREATE TABLE IF NOT EXISTS sheet_attributes (
  sheet_id INTEGER REFERENCES sheet_metadata(sheet_id),
  key TEXT,
  value TEXT,
  embedding_text TEXT,
  vector BLOB,
  PRIMARY KEY (sheet_id, key)
);
"""
                )
            )
            
            conn.execute(
                text(
                    """
CREATE TABLE IF NOT EXISTS row_vectors (
  sheet_id INTEGER,
  rowid INTEGER,
  file_id INTEGER,
  table_name TEXT,
  vector BLOB,
  text TEXT,
  PRIMARY KEY (sheet_id, rowid),
  FOREIGN KEY (sheet_id) REFERENCES sheet_metadata(sheet_id),
  FOREIGN KEY (file_id) REFERENCES file_metadata(file_id)
);
"""
                )
            )

            # ---------------- 创建 FTS5 虚拟表用于 BM25 检索 ----------------
            # 使用 external content table 方式保持与 row_vectors 同步
            conn.execute(
                text(
                    """
CREATE VIRTUAL TABLE IF NOT EXISTS row_vectors_fts
USING fts5(text, content='row_vectors');
"""
                )
            )

            # 建立触发器，保持 row_vectors 与 FTS 索引实时同步
            conn.execute(
                text(
                    """
CREATE TRIGGER IF NOT EXISTS row_vectors_ai AFTER INSERT ON row_vectors BEGIN
  INSERT INTO row_vectors_fts(rowid, text) VALUES (new.rowid, new.text);
END;
"""
                )
            )

            conn.execute(
                text(
                    """
CREATE TRIGGER IF NOT EXISTS row_vectors_ad AFTER DELETE ON row_vectors BEGIN
  DELETE FROM row_vectors_fts WHERE rowid = old.rowid;
END;
"""
                )
            )

            conn.execute(
                text(
                    """
CREATE TRIGGER IF NOT EXISTS row_vectors_au AFTER UPDATE ON row_vectors BEGIN
  UPDATE row_vectors_fts SET text = new.text WHERE rowid = new.rowid;
END;
"""
                )
            )

            # ---- 首次创建 FTS5 时，回填现有 row_vectors 数据 ----------------
            try:
                row_count_row = conn.execute(text("SELECT count(*) FROM row_vectors_fts")).fetchone()
                cnt_fts = row_count_row[0] if row_count_row else 0
                if cnt_fts == 0:
                    conn.execute(
                        text(
                            "INSERT INTO row_vectors_fts(rowid, text) SELECT rowid, text FROM row_vectors"
                        )
                    )
            except Exception:
                # 若 FTS 表未初始化等异常，忽略
                pass

            # 检查旧表是否存在，如果存在则创建临时表以备迁移
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            old_tables_exist = "column_vectors" in tables
            
            # 检查 file_metadata 结构是否是旧版（有 description 和 vector 列）
            old_file_metadata_structure = False
            if "file_metadata" in tables:
                file_meta_cols = {col["name"] for col in inspector.get_columns("file_metadata")}
                if "description" in file_meta_cols and "vector" in file_meta_cols:
                    old_file_metadata_structure = True
            
            # 检查 sheet_metadata 结构是否是旧版（有 description 和 vector 列）
            old_sheet_metadata_structure = False
            if "sheet_metadata" in tables:
                sheet_meta_cols = {col["name"] for col in inspector.get_columns("sheet_metadata")}
                if "description" in sheet_meta_cols and "vector" in sheet_meta_cols:
                    old_sheet_metadata_structure = True
            
            # 如果存在旧结构，则提示进行迁移
            if old_tables_exist or old_file_metadata_structure or old_sheet_metadata_structure:
                print("检测到旧版数据库结构，建议运行数据迁移: db_writer.migrate_old_schema()")

            # ---- 若旧库缺少 status 列则在线迁移 --------------------------------
            cols_file_meta = {col["name"] for col in inspector.get_columns("file_metadata")}
            if "status" not in cols_file_meta:
                conn.execute(text("ALTER TABLE file_metadata ADD COLUMN status TEXT DEFAULT 'pending'"))
            cols_sheet_meta = {col["name"] for col in inspector.get_columns("sheet_metadata")}
            if "status" not in cols_sheet_meta:
                conn.execute(text("ALTER TABLE sheet_metadata ADD COLUMN status TEXT DEFAULT 'pending'"))
    
    # --- 数据迁移 ----------------------------------------------------
    def migrate_old_schema(self) -> None:  # noqa: D401
        """迁移旧版数据库结构到新版层次化存储结构。"""
        inspector = inspect(self.engine)
        tables = inspector.get_table_names()
        
        with self.engine.begin() as conn:
            # 迁移 file_metadata 的 description 和 vector
            if "file_metadata" in tables:
                file_meta_cols = {col["name"] for col in inspector.get_columns("file_metadata")}
                if "description" in file_meta_cols and "vector" in file_meta_cols:
                    print("正在迁移 file_metadata 表...")
                    # 读取旧数据
                    result = conn.execute(text(
                        "SELECT file_id, description, vector FROM file_metadata "
                        "WHERE description IS NOT NULL OR vector IS NOT NULL"
                    ))
                    
                    for file_id, description, vector_blob in result.fetchall():
                        if description:
                            conn.execute(text(
                                "INSERT OR REPLACE INTO file_attributes (file_id, key, value, embedding_text, vector) "
                                "VALUES (:file_id, 'description', :value, :embedding_text, :vector)"
                            ), {"file_id": file_id, "value": description, "embedding_text": description, "vector": vector_blob})
            
            # 迁移 sheet_metadata 的 description 和 vector
            if "sheet_metadata" in tables:
                sheet_meta_cols = {col["name"] for col in inspector.get_columns("sheet_metadata")}
                if "description" in sheet_meta_cols and "vector" in sheet_meta_cols:
                    print("正在迁移 sheet_metadata 表...")
                    # 读取旧数据
                    result = conn.execute(text(
                        "SELECT sheet_id, description, vector FROM sheet_metadata "
                        "WHERE description IS NOT NULL OR vector IS NOT NULL"
                    ))
                    
                    for sheet_id, description, vector_blob in result.fetchall():
                        if description:
                            conn.execute(text(
                                "INSERT OR REPLACE INTO sheet_attributes (sheet_id, key, value, embedding_text, vector) "
                                "VALUES (:sheet_id, 'description', :value, :embedding_text, :vector)"
                            ), {"sheet_id": sheet_id, "value": description, "embedding_text": description, "vector": vector_blob})
            
            # 迁移 column_vectors 表到 sheet_attributes
            if "column_vectors" in tables:
                print("正在迁移 column_vectors 表...")
                # 复制到临时表
                conn.execute(text("CREATE TABLE IF NOT EXISTS temp_column_vectors AS SELECT * FROM column_vectors"))
                
                # 读取旧数据并迁移
                result = conn.execute(text(
                    "SELECT sheet_id, column_name, text, vector FROM column_vectors"
                ))
                
                for sheet_id, column_name, text_value, vector_blob in result.fetchall():
                    if column_name == "__sheet_desc__":
                        key = "original_description"
                        embedding_text = text_value
                    else:
                        key = f"column:{column_name}"
                        embedding_text = f"{column_name}: {text_value}"
                        
                    if text_value:
                        conn.execute(text(
                            "INSERT OR REPLACE INTO sheet_attributes (sheet_id, key, value, embedding_text, vector) "
                            "VALUES (:sheet_id, :key, :value, :embedding_text, :vector)"
                        ), {"sheet_id": sheet_id, "key": key, "value": text_value, "embedding_text": embedding_text, "vector": vector_blob})
            
            print("迁移完成。请运行向量同步工具重新构建向量库: python -m src.vectorization.sync_chroma")
    
    # --- File attributes ----------------------------------------------
    
    def insert_file_attributes(self, file_id: int, attributes: Dict[str, str], *, vectors: Optional[Dict[str, List[float]]] = None, embedding_texts: Optional[Dict[str, str]] = None) -> None:  # noqa: D401
        """批量插入 file_attributes 记录。"""
        if not attributes:
            return
            
        records = []
        for key, value in attributes.items():
            vector_blob = None
            if vectors and key in vectors and vectors[key]:
                vector_blob = self._vector_to_blob(vectors[key])
            
            emb_text = embedding_texts.get(key, value) if embedding_texts else value
                
            records.append({
                "file_id": file_id,
                "key": key,
                "value": value,
                "embedding_text": emb_text,
                "vector": vector_blob
            })
            
        with self.engine.begin() as conn:
            conn.execute(
                text(
                    """
INSERT OR REPLACE INTO file_attributes (file_id, key, value, embedding_text, vector)
VALUES (:file_id, :key, :value, :embedding_text, :vector)
"""
                ),
                records,
            )
    
    # --- Sheet attributes ----------------------------------------------
    
    def insert_sheet_attributes(self, sheet_id: int, attributes: Dict[str, str], *, vectors: Optional[Dict[str, List[float]]] = None, embedding_texts: Optional[Dict[str, str]] = None) -> None:  # noqa: D401
        """批量插入 sheet_attributes 记录。"""
        if not attributes:
            return
            
        records = []
        for key, value in attributes.items():
            vector_blob = None
            if vectors and key in vectors and vectors[key]:
                vector_blob = self._vector_to_blob(vectors[key])
            
            emb_text = embedding_texts.get(key, value) if embedding_texts else value
                
            records.append({
                "sheet_id": sheet_id,
                "key": key,
                "value": value,
                "embedding_text": emb_text,
                "vector": vector_blob
            })
            
        with self.engine.begin() as conn:
            conn.execute(
                text(
                    """
INSERT OR REPLACE INTO sheet_attributes (sheet_id, key, value, embedding_text, vector)
VALUES (:sheet_id, :key, :value, :embedding_text, :vector)
"""
                ),
                records,
            )

    # --- Column vectors - 保留向后兼容 ----------------------------------------

    def insert_column_vectors(self, records: List[dict]) -> None:  # noqa: D401
        """批量插入 column_vectors 记录（向后兼容）。"""
        if not records:
            return
            
        # 将 column_vectors 转换为 sheet_attributes
        sheet_attributes = {}
        sheet_vectors = {}
        sheet_embedding_texts = {}
        
        for record in records:
            sheet_id = record.get("sheet_id")
            column_name = record.get("column_name")
            value = record.get("text", "")
            
            if sheet_id not in sheet_attributes:
                sheet_attributes[sheet_id] = {}
                sheet_vectors[sheet_id] = {}
                sheet_embedding_texts[sheet_id] = {}
                
            # 特殊处理 __sheet_desc__
            if column_name == "__sheet_desc__":
                key = "description"
                embedding_text = value
            else:
                key = f"column:{column_name}"
                embedding_text = f"{column_name}: {value}"
                
            sheet_attributes[sheet_id][key] = value
            sheet_embedding_texts[sheet_id][key] = embedding_text
            
            # 解析向量
            if "vector" in record and record["vector"]:
                try:
                    vector_list = list(array("f", record["vector"]).tobytes())
                    sheet_vectors[sheet_id][key] = vector_list
                except Exception:
                    pass
        
        # 调用新接口插入
        for sheet_id in sheet_attributes:
            self.insert_sheet_attributes(
                sheet_id, 
                sheet_attributes[sheet_id], 
                vectors=sheet_vectors.get(sheet_id),
                embedding_texts=sheet_embedding_texts.get(sheet_id)
            )

    # --- Utility --------------------------------------------------------
    def table_exists(self, name: str) -> bool:  # noqa: D401
        inspector = inspect(self.engine)
        return name in inspector.get_table_names() 

    # --- Metadata upsert ------------------------------------------------

    def upsert_file_metadata(
        self,
        file_path: str,
        *,
        attributes: Optional[Dict[str, str]] = None,
        vectors: Optional[Dict[str, List[float]]] = None,
        embedding_texts: Optional[Dict[str, str]] = None,
        status: str = "pending",
    ) -> int:  # noqa: D401
        """插入或更新 file_metadata，并返回 file_id。"""
        with self.engine.begin() as conn:
            conn.execute(
                text(
                    """
INSERT INTO file_metadata (file_path, status)
VALUES (:file_path, :status)
ON CONFLICT(file_path) DO UPDATE SET
  status = excluded.status
;
"""
                ),
                {"file_path": file_path, "status": status},
            )
            res = conn.execute(
                text("SELECT file_id FROM file_metadata WHERE file_path = :file_path"),
                {"file_path": file_path},
            )
            file_id = int(res.scalar_one())
            
        # 插入属性
        if attributes:
            self.insert_file_attributes(file_id, attributes, vectors=vectors, embedding_texts=embedding_texts)
            
        return file_id

    def upsert_sheet_metadata(
        self,
        file_id: int,
        sheet_name: str,
        *,
        table_name: Optional[str] = None,
        attributes: Optional[Dict[str, str]] = None,
        vectors: Optional[Dict[str, List[float]]] = None,
        embedding_texts: Optional[Dict[str, str]] = None,
        status: str = "pending",
    ) -> int:  # noqa: D401
        """插入或更新 sheet_metadata，并返回 sheet_id。"""
        with self.engine.begin() as conn:
            conn.execute(
                text(
                    """
INSERT INTO sheet_metadata (file_id, sheet_name, table_name, status)
VALUES (:file_id, :sheet_name, :table_name, :status)
ON CONFLICT(file_id, sheet_name) DO UPDATE SET
  table_name=coalesce(excluded.table_name, sheet_metadata.table_name),
  status=excluded.status;
"""
                ),
                {
                    "file_id": file_id,
                    "sheet_name": sheet_name,
                    "table_name": table_name,
                    "status": status,
                },
            )
            res = conn.execute(
                text(
                    "SELECT sheet_id FROM sheet_metadata WHERE file_id = :file_id AND sheet_name = :sheet_name"
                ),
                {"file_id": file_id, "sheet_name": sheet_name},
            )
            sheet_id = int(res.scalar_one())
        
        # 插入属性
        if attributes:
            self.insert_sheet_attributes(sheet_id, attributes, vectors=vectors, embedding_texts=embedding_texts)
            
        return sheet_id

    # --- Static util ---------------------------------------------------

    @staticmethod
    def _vector_to_blob(vector: Optional[List[float]]) -> Optional[bytes]:  # noqa: D401
        if vector is None:
            return None
        return array("f", vector).tobytes() 

    def get_file_id(self, file_path: str) -> Optional[int]:  # noqa: D401
        """若文件已存在于 file_metadata，返回其 file_id，否则返回 ``None``。"""
        with self.engine.begin() as conn:
            res = conn.execute(
                text("SELECT file_id FROM file_metadata WHERE file_path = :file_path"),
                {"file_path": file_path},
            )
            row = res.fetchone()
            return int(row[0]) if row else None

    def get_max_sheet_id(self) -> int:  # noqa: D401
        """返回当前 sheet_metadata 中的最大 sheet_id，若表为空则返回 0。"""
        with self.engine.begin() as conn:
            res = conn.execute(text("SELECT COALESCE(MAX(sheet_id), 0) FROM sheet_metadata"))
            return int(res.scalar_one())

    def set_file_status(self, file_id: int, status: str = "done") -> None:  # noqa: D401
        """更新 file_metadata.status"""
        with self.engine.begin() as conn:
            conn.execute(
                text("UPDATE file_metadata SET status = :status WHERE file_id = :file_id"),
                {"status": status, "file_id": file_id},
            )

    def set_sheet_status(self, sheet_id: int, status: str = "done") -> None:  # noqa: D401
        """更新 sheet_metadata.status"""
        with self.engine.begin() as conn:
            conn.execute(
                text("UPDATE sheet_metadata SET status = :status WHERE sheet_id = :sheet_id"),
                {"status": status, "sheet_id": sheet_id},
            )

    def get_file_info(self, file_path: str) -> Optional[tuple[int, str]]:  # noqa: D401
        """返回 (file_id, status)；若不存在则返回 None"""
        with self.engine.begin() as conn:
            res = conn.execute(
                text("SELECT file_id, status FROM file_metadata WHERE file_path = :file_path"),
                {"file_path": file_path},
            )
            row = res.fetchone()
            return (int(row[0]), str(row[1])) if row else None

    # ------------------------------------------------------------------
    # Orphan cleanup helpers
    # ------------------------------------------------------------------

    def cleanup_orphan_records(self) -> None:  # noqa: D401
        """删除 file_attributes / sheet_attributes / row_vectors 中的孤立记录。

        孤立记录指：其外键引用的文件或 Sheet 在主元数据表中已不存在。
        在断点续传或人工删除元数据表记录后，可调用本方法进行一次性清理。
        """
        with self.engine.begin() as conn:
            # file_attributes -> file_metadata
            conn.execute(
                text(
                    """
DELETE FROM file_attributes
WHERE file_id NOT IN (SELECT file_id FROM file_metadata);
"""
                )
            )

            # sheet_attributes -> sheet_metadata
            conn.execute(
                text(
                    """
DELETE FROM sheet_attributes
WHERE sheet_id NOT IN (SELECT sheet_id FROM sheet_metadata);
"""
                )
            )

            # row_vectors -> sheet_metadata
            conn.execute(
                text(
                    """
DELETE FROM row_vectors
WHERE sheet_id NOT IN (SELECT sheet_id FROM sheet_metadata);
"""
                )
            )

            # 由于 row_vectors 同时持有 file_id，也清理 file_id 不存在的情况
            conn.execute(
                text(
                    """
DELETE FROM row_vectors
WHERE file_id NOT IN (SELECT file_id FROM file_metadata);
"""
                )
            ) 