from __future__ import annotations
import time
import os
from typing import Dict, List, Protocol
import logging

"""
VectorStore 抽象层，隐藏具体向量数据库实现差异。
默认实现使用 Chroma（duckdb + parquet 持久化）。
"""


# Disable ChromaDB anonymized telemetry (see https://docs.trychroma.com/docs/overview/telemetry)
os.environ.setdefault("ANONYMIZED_TELEMETRY", "FALSE")

logger = logging.getLogger(__name__)

try:
    import chromadb  # type: ignore
    try:
        from chromadb.config import Settings  # type: ignore
    except Exception:  # 新版本 chromadb 移除 Settings
        Settings = None  # type: ignore
except ImportError:  # pragma: no cover
    chromadb = None  # type: ignore


class VectorStore(Protocol):  # noqa: D101
    def add_embeddings(self, ids: List[str], embeddings: List[List[float]], metadata: List[Dict]) -> None: ...
    def query(self, embedding: List[float], top_k: int = 10, filter: Dict | None = None) -> List[str]: ...
    def delete(self, ids: List[str]) -> None: ...


class ChromaVectorStore:  # noqa: D101
    def __init__(self, collection_name: str = "sheet_vectors", persist_directory: str = "vector_store") -> None:
        if chromadb is None:
            raise ImportError("需要安装 chromadb 才能使用 ChromaVectorStore")

        # 兼容 v0.x 与 v1.x API
        if hasattr(chromadb, "PersistentClient"):
            self.client = chromadb.PersistentClient(path=persist_directory)
        else:
            if Settings is None:
                raise ImportError("当前 chromadb 版本不兼容，建议使用 0.4.x 或安装 chroma-migrate")
            # Explicitly turn off Chroma anonymized telemetry when using legacy (<0.5) client
            self.client = chromadb.Client(
                Settings(
                    chroma_db_impl="duckdb+parquet",
                    persist_directory=persist_directory,
                    anonymized_telemetry=False,
                )
            )
        self.collection = self.client.get_or_create_collection(collection_name)

        # 重试配置
        self._max_retries = 3
        self._backoff = 1.5

    # --- 内部通用重试装饰器 -------------------------------------------

    def _with_retry(self, func, *args, **kwargs):  # type: ignore
        attempt = 0
        while True:
            try:
                return func(*args, **kwargs)
            except Exception as exc:  # noqa: BLE001
                attempt += 1
                if attempt > self._max_retries:
                    logger.error("VectorStore 操作重试超限: %s", exc)
                    raise
                wait = self._backoff ** (attempt - 1)
                logger.warning("VectorStore 操作异常，第 %s 次重试 %.2fs 后继续", attempt, wait)
                time.sleep(wait)

    # --- 实现 VectorStore 协议 ----------------------------------------
    def add_embeddings(self, ids: List[str], embeddings: List[List[float]], metadata: List[Dict]) -> None:  # noqa: D401
        self._with_retry(
            self.collection.add, ids=ids, embeddings=embeddings, metadatas=metadata  # type: ignore[arg-type]
        )

    def query(self, embedding: List[float], top_k: int = 10, filter: Dict | None = None) -> List[str]:  # noqa: D401
        results = self._with_retry(
            self.collection.query,
            query_embeddings=[embedding],
            n_results=top_k,
            where=filter,  # type: ignore[arg-type]
        )
        return results["ids"][0] if results["ids"] else []

    def delete(self, ids: List[str]) -> None:  # noqa: D401
        self._with_retry(self.collection.delete, ids=ids)  # type: ignore[arg-type] 