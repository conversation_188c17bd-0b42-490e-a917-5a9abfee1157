from __future__ import annotations
import json
import logging
import os
import time
from typing import List

from requests import RequestException

import requests

from src.utils.config import load_config, get_task_cfg  # lazy import ok here, small file

"""
Vectorizer：封装 Ollama /api/embeddings 接口。
默认模型见 docs/excel_ingestion_design.md（bge-m3:latest）。
"""

logger = logging.getLogger(__name__)

class Vectorizer:  # noqa: D101
    def __init__(
        self,
        host: str | None = None,
        model: str | None = None,
        *,
        max_retries: int = 3,
        backoff_factor: float = 1.5,
    ) -> None:
        """Vectorizer constructor with configurable host & model.

        Resolution order for *host*:
          1) explicit argument
          2) environment variable ``OLLAMA_HOST``
          3) config: ``llm_services.ollama.host`` (fallback)
          4) "http://localhost:11434"

        Resolution order for *model*:
          1) explicit argument
          2) config task ``tasks.embedding.model``
          3) default "bge-m3:latest"
        """

        cfg_task = get_task_cfg("embedding")

        if model is None:
            model = cfg_task.get("model", "bge-m3:latest")

        if host is None:
            host = os.getenv("OLLAMA_HOST")
            if host is None:
                cfg = load_config()
                # Prefer new llm_services.ollama.host
                host = (
                    cfg.get("llm_services", {})  # type: ignore[arg-type]
                    .get("ollama", {})
                    .get("host")
                ) or cfg.get("ollama_host") or cfg.get("llm", {}).get("ollama_host") or "http://localhost:11434"

        assert isinstance(host, str)

        self.endpoint = f"{host.rstrip('/')}/api/embeddings"
        self.model = model
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor

    def embed_texts(self, texts: List[str]) -> List[List[float]]:  # noqa: D401
        """批量生成向量。当前简单串行实现，后续可批量化。"""
        embeddings: List[List[float]] = []
        for text in texts:
            payload = {"model": self.model, "prompt": text}
            attempt = 0
            while True:
                try:
                    # Skip environment proxies to avoid SOCKS errors when `HTTP_PROXY`/`ALL_PROXY` is set
                    resp = requests.post(
                        self.endpoint,
                        data=json.dumps(payload),
                        timeout=30,
                        proxies={"http": None, "https": None},  # type: ignore[arg-type]
                    )
                    resp.raise_for_status()
                    data = resp.json()
                    embeddings.append(data["embedding"])
                    break
                except (RequestException, KeyError, ValueError) as ex:  # noqa: BLE001
                    attempt += 1
                    if attempt > self.max_retries:
                        logger.error("向量化重试超限，文本被跳过: %s", ex)
                        embeddings.append([])
                        break
                    wait = self.backoff_factor ** (attempt - 1)
                    logger.warning("向量化失败，第 %s 次重试将在 %.2fs 后进行", attempt, wait)
                    time.sleep(wait)
        return embeddings


# 顶级函数接口

def embed_texts(texts: List[str]) -> List[List[float]]:  # noqa: D401
    return Vectorizer().embed_texts(texts) 