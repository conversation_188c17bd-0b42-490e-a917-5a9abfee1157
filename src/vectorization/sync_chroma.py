"""
同步脚本：将 SQLite (data.db) 中的向量批量导入 Chroma 向量库。

用途：当需要重建 / 迁移 Chroma 索引，或首次部署时，可先跑解析流程（Excel→SQLite），随后运行：

    # 在项目根目录下
    python -m src.vectorization.sync_chroma --db output/data.db --persist vector_store

如已将 `src/` 目录加入 `PYTHONPATH`，亦可使用：

    python -m vectorization.sync_chroma --db output/data.db

默认一次批量写入 1024 条，遇到重复 id 会自动跳过。
"""

from __future__ import annotations

import argparse
import logging
import sqlite3
from array import array
from pathlib import Path
from typing import List, Dict

from .vector_store import ChromaVectorStore

logger = logging.getLogger(__name__)


# ---------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------

def _blob_to_vector(blob: bytes | None) -> List[float]:  # noqa: D401
    """将 SQLite BLOB 转回 float32 list。若为空返回 []。"""
    if blob is None:
        return []
    arr = array("f")
    arr.frombytes(blob)
    return arr.tolist()


def _add_in_batches(
    store: ChromaVectorStore,
    ids: List[str],
    embeddings: List[List[float]],
    metas: List[Dict],
    *,
    batch_size: int = 1024,
) -> None:  # noqa: D401
    """按照 batch_size 写入 Chroma；若 id 已存在则忽略。"""
    total = len(ids)
    for i in range(0, total, batch_size):
        batch_ids = ids[i : i + batch_size]
        batch_vecs = embeddings[i : i + batch_size]
        batch_meta = metas[i : i + batch_size]
        try:
            store.add_embeddings(batch_ids, batch_vecs, batch_meta)  # type: ignore[arg-type]
        except Exception as exc:  # noqa: BLE001
            logger.warning("批量写入 Chroma 失败，可能存在重复 id：%s", exc)
            # 粗暴降级：逐条尝试，跳过冲突的 id
            for j, rid in enumerate(batch_ids):
                try:
                    store.add_embeddings([rid], [batch_vecs[j]], [batch_meta[j]])  # type: ignore[arg-type]
                except Exception:
                    continue


# ---------------------------------------------------------------------
# Core sync logic
# ---------------------------------------------------------------------

def sync_sqlite_to_chroma(
    db_path: Path | str = "output/data.db",
    *,
    persist_directory: Path | str = "vector_store",
    batch_size: int = 1024,
) -> None:  # noqa: D401
    """读取 SQLite 向量表，批量导入 Chroma。"""

    db_path = Path(db_path)
    if not db_path.exists():
        raise FileNotFoundError(f"SQLite 数据库不存在: {db_path}")

    # 为不同层级单独使用集合，避免混住
    store_file = ChromaVectorStore(collection_name="file_vectors", persist_directory=str(persist_directory))
    store_sheet = ChromaVectorStore(collection_name="sheet_vectors", persist_directory=str(persist_directory))
    store_row = ChromaVectorStore(collection_name="row_vectors", persist_directory=str(persist_directory))

    conn = sqlite3.connect(db_path)
    cur = conn.cursor()

    # ---- 文件级属性 ------------------------------------------------------
    logger.info("同步 file_attributes ...")
    cur.execute("""
    SELECT fm.file_id, fa.key, fa.embedding_text, fa.vector 
    FROM file_metadata fm 
    JOIN file_attributes fa ON fm.file_id = fa.file_id
    WHERE fa.vector IS NOT NULL
    """)
    rows = cur.fetchall()
    ids, vecs, metas = [], [], []
    for file_id, key, text, blob in rows:
        vec = _blob_to_vector(blob)
        if not vec:
            continue
        ids.append(f"file:{file_id}:{key}")
        vecs.append(vec)
        metas.append({"file_id": file_id, "level": "file", "key": key, "text": text})
    _add_in_batches(store_file, ids, vecs, metas, batch_size=batch_size)

    # ---- Sheet 级属性 ----------------------------------------------------
    logger.info("同步 sheet_attributes ...")
    cur.execute("""
    SELECT sm.sheet_id, sm.file_id, sa.key, sa.embedding_text, sa.vector 
    FROM sheet_metadata sm 
    JOIN sheet_attributes sa ON sm.sheet_id = sa.sheet_id
    WHERE sa.vector IS NOT NULL
    """)
    rows = cur.fetchall()
    ids, vecs, metas = [], [], []
    for sheet_id, file_id, key, text, blob in rows:
        vec = _blob_to_vector(blob)
        if not vec:
            continue
        
        # 特殊处理一些关键属性
        if key == "description":
            # 保持原有的 sheet:{id} 格式兼容性
            ids.append(f"sheet:{sheet_id}")
        elif key == "original_description":
            # 保持原有的 sheet_desc:{id} 格式兼容性
            ids.append(f"sheet_desc:{sheet_id}")
        else:
            # 新的格式: sheet:{id}:{key}
            ids.append(f"sheet:{sheet_id}:{key}")
            
        vecs.append(vec)
        metas.append({"sheet_id": sheet_id, "file_id": file_id, "level": "sheet", "key": key, "text": text})
    _add_in_batches(store_sheet, ids, vecs, metas, batch_size=batch_size)

    # ---- 处理旧的 column_vectors 表（如果存在） -------------------------------
    if cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='temp_column_vectors'").fetchone():
        logger.info("同步旧的 column_vectors 表 (从临时表) ...")
        cur.execute("""
        SELECT sheet_id, file_id, table_name, column_name, vector, text 
        FROM temp_column_vectors 
        WHERE vector IS NOT NULL
        """)
        rows = cur.fetchall()
        ids, vecs, metas = [], [], []
        for sheet_id, file_id, table_name, column_name, blob, text in rows:
            vec = _blob_to_vector(blob)
            if not vec:
                continue

            if column_name == "__sheet_desc__":
                # 已由 sheet_attributes 处理，跳过
                continue
            else:
                vid = f"const:{table_name}:{column_name}"
                level = "column_constant"

            ids.append(vid)
            vecs.append(vec)
            metas.append({
                "sheet_id": sheet_id,
                "file_id": file_id,
                "table_name": table_name,
                "column_name": column_name,
                "level": level,
            })

        if ids:
            _add_in_batches(store_sheet, ids, vecs, metas, batch_size=batch_size)

    # ---- 行级 --------------------------------------------------------
    logger.info("同步 row_vectors ... (批量 %s)", batch_size)
    cur.execute(
        "SELECT sheet_id, rowid, file_id, table_name, vector, text FROM row_vectors WHERE vector IS NOT NULL"
    )
    while True:
        rows = cur.fetchmany(batch_size)
        if not rows:
            break
        ids, vecs, metas = [], [], []
        for sheet_id, rowid, file_id, table_name, blob, row_text in rows:
            vec = _blob_to_vector(blob)
            if not vec:
                continue
            ids.append(f"{sheet_id}:{rowid}")
            vecs.append(vec)
            metas.append({
                "sheet_id": sheet_id,
                "file_id": file_id,
                "table_name": table_name,
                "rowid": rowid,
                "level": "row",
                "text": row_text,
            })
        if ids:
            _add_in_batches(store_row, ids, vecs, metas, batch_size=batch_size)

    conn.close()
    logger.info("同步完成 ✅")


# ---------------------------------------------------------------------
# CLI
# ---------------------------------------------------------------------

def _parse_args() -> argparse.Namespace:  # noqa: D401
    parser = argparse.ArgumentParser(description="将 SQLite 向量导入 Chroma VectorStore")
    parser.add_argument("--db", type=Path, default=Path("output/data.db"), help="SQLite 数据库路径")
    parser.add_argument("--persist", type=Path, default=Path("vector_store"), help="Chroma 持久化目录")
    parser.add_argument("--batch", type=int, default=1024, help="批量写入大小")
    parser.add_argument("--verbose", action="store_true", help="输出调试日志")
    return parser.parse_args()


def main() -> None:  # noqa: D401
    args = _parse_args()
    logging.basicConfig(level=logging.DEBUG if args.verbose else logging.INFO)
    sync_sqlite_to_chroma(db_path=args.db, persist_directory=args.persist, batch_size=args.batch)


if __name__ == "__main__":  # pragma: no cover
    main() 