# backfill_vectors.py

"""批量补写缺失向量，并可选同步到 Chroma。

用法示例::

    # 仅补写向量，不同步 Chroma
    python -m src.vectorization.backfill_vectors --db output/data.db

    # 补写后同步到 Chroma（推荐）
    python -m src.vectorization.backfill_vectors --db output/data.db --sync

参数说明
----------
--db        SQLite 数据库路径，默认 ``output/data.db``
--batch     向量化批量大小，默认 256
--sync      在向量补写完成后，自动调用 :pymod:`src.vectorization.sync_chroma` 将最新向量写入 Chroma
--persist   Chroma 持久化目录，配合 ``--sync`` 使用，默认 ``vector_store``
"""

from __future__ import annotations

import argparse
import logging
import sqlite3
from array import array
from pathlib import Path
from typing import List

from .vectorizer import Vectorizer
# 注意：本模块不直接调用 ChromaVectorStore，但保留引用注释供类型检查
# from .vector_store import ChromaVectorStore  # noqa: F401
from .sync_chroma import sync_sqlite_to_chroma

# 👉 新增 rich 相关导入
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    BarColumn,
    MofNCompleteColumn,
    TimeElapsedColumn,
    TextColumn,
)

# 👉 新增 Console 实例（全局复用）
console = Console()

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
# Helper
# ---------------------------------------------------------------------------


def _vector_to_blob(vec: List[float] | None) -> bytes | None:  # noqa: D401
    """Float list → BLOB; `None` or empty list 返回 ``None``。"""
    if not vec:
        return None
    return array("f", vec).tobytes()


# ---------------------------------------------------------------------------
# Logging helper
# ---------------------------------------------------------------------------


def _setup_logging(verbose: bool = False) -> None:  # noqa: D401
    """配置日志，仅写入 ``logs/backfill_vectors.log``。"""
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    log_level = logging.DEBUG if verbose else logging.INFO

    file_handler = logging.FileHandler(logs_dir / "backfill_vectors.log", encoding="utf-8")
    file_handler.setLevel(log_level)
    file_handler.setFormatter(
        logging.Formatter("%(asctime)s [%(levelname)s] %(name)s: %(message)s")
    )

    root_logger = logging.getLogger()
    root_logger.handlers.clear()  # 移除任何已有处理器，防止输出到控制台
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)


# ---------------------------------------------------------------------------
# Core backfill logic
# ---------------------------------------------------------------------------


def backfill_vectors(
    *,
    db_path: Path | str = "output/data.db",
    batch_size: int = 256,
) -> None:  # noqa: D401
    """扫描三张表的向量缺口并补写：``file_attributes``、``sheet_attributes``、``row_vectors``。"""

    db_path = Path(db_path)
    if not db_path.exists():
        raise FileNotFoundError(f"SQLite 数据库不存在: {db_path}")

    vec = Vectorizer()
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()

    # ---------------- File attributes ----------------------------------
    console.rule("[bold cyan]file_attributes.vector")
    logger.info("补写 file_attributes.vector …")
    rows_processed = 0
    while True:
        cur.execute(
            "SELECT file_id, key, embedding_text FROM file_attributes WHERE vector IS NULL LIMIT ?",
            (batch_size,),
        )
        rows = cur.fetchall()
        if not rows:
            break

        texts = [txt or "" for _, _, txt in rows]
        embeddings = vec.embed_texts(texts)

        for (file_id, key, _), emb in zip(rows, embeddings):
            blob = _vector_to_blob(emb)
            cur.execute(
                "UPDATE file_attributes SET vector = ? WHERE file_id = ? AND key = ?",
                (blob, file_id, key),
            )
        conn.commit()
        rows_processed += len(rows)
        console.print(f"[green]file_attributes 批量写入 {len(rows)} 条 (已提交)[/green]")

    if rows_processed == 0:
        console.print("[yellow]file_attributes 已无缺失向量[/yellow]")
    else:
        logger.info("已写入 %s 条 file_attributes 向量", rows_processed)

    # ---------------- Sheet attributes ---------------------------------
    console.rule("[bold cyan]sheet_attributes.vector")
    logger.info("补写 sheet_attributes.vector …")
    rows_processed = 0
    while True:
        cur.execute(
            "SELECT sheet_id, key, embedding_text FROM sheet_attributes WHERE vector IS NULL LIMIT ?",
            (batch_size,),
        )
        rows = cur.fetchall()
        if not rows:
            break

        texts = [txt or "" for _, _, txt in rows]
        embeddings = vec.embed_texts(texts)

        for (sheet_id, key, _), emb in zip(rows, embeddings):
            blob = _vector_to_blob(emb)
            cur.execute(
                "UPDATE sheet_attributes SET vector = ? WHERE sheet_id = ? AND key = ?",
                (blob, sheet_id, key),
            )
        conn.commit()
        rows_processed += len(rows)
        console.print(f"[green]sheet_attributes 批量写入 {len(rows)} 条 (已提交)[/green]")

    if rows_processed == 0:
        console.print("[yellow]sheet_attributes 已无缺失向量[/yellow]")
    else:
        logger.info("已写入 %s 条 sheet_attributes 向量", rows_processed)

    # ---------------- Row vectors --------------------------------------
    console.rule("[bold cyan]row_vectors.vector")
    logger.info("补写 row_vectors.vector …")
    # 统计剩余待处理条数，以便显示整体进度条
    cur.execute("SELECT COUNT(*) FROM row_vectors WHERE vector IS NULL")
    total_missing = cur.fetchone()[0] or 0

    if total_missing == 0:
        console.print("[yellow]row_vectors 已无缺失向量[/yellow]")
    else:
        rows_processed = 0
        with Progress(
            SpinnerColumn(),
            TextColumn("{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeElapsedColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("向量化+写入", total=total_missing)

            while True:
                cur.execute(
                    "SELECT sheet_id, rowid, text FROM row_vectors WHERE vector IS NULL LIMIT ?",
                    (batch_size,),
                )
                rows = cur.fetchall()
                if not rows:
                    break

                texts = [txt or "" for _, _, txt in rows]
                embeddings = vec.embed_texts(texts)

                for (sheet_id, rowid, _), emb in zip(rows, embeddings):
                    blob = _vector_to_blob(emb)
                    cur.execute(
                        "UPDATE row_vectors SET vector = ? WHERE sheet_id = ? AND rowid = ?",
                        (blob, sheet_id, rowid),
                    )
                conn.commit()
                rows_processed += len(rows)
                progress.update(task, advance=len(rows))

        logger.info("已写入 %s 条 row_vectors 向量", rows_processed)

    conn.close()
    console.print("[bold green]向量补写完成 ✅[/bold green]")
    logger.info("向量补写完成 ✅")


# ---------------------------------------------------------------------------
# CLI
# ---------------------------------------------------------------------------


def _parse_args() -> argparse.Namespace:  # noqa: D401
    p = argparse.ArgumentParser("Backfill missing vectors in SQLite DB")
    p.add_argument("--db", type=Path, default=Path("output/data.db"), help="SQLite 数据库路径")
    p.add_argument("--batch", type=int, default=256, help="向量化批量大小")
    p.add_argument("--sync", action="store_true", help="补写后同步到 Chroma VectorStore")
    p.add_argument("--persist", type=Path, default=Path("vector_store"), help="Chroma 持久化目录")
    p.add_argument("--verbose", action="store_true", help="输出调试日志")
    return p.parse_args()


def main() -> None:  # noqa: D401
    args = _parse_args()
    _setup_logging(args.verbose)

    backfill_vectors(db_path=args.db, batch_size=args.batch)

    if args.sync:
        logger.info("开始同步至 Chroma VectorStore …")
        sync_sqlite_to_chroma(db_path=args.db, persist_directory=args.persist)
        logger.info("同步完成 ✅")


if __name__ == "__main__":  # pragma: no cover
    main() 