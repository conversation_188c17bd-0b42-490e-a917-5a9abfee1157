import os
import json
import re
import logging
from logging.handlers import RotatingFileHandler
import textwrap
from copy import deepcopy
from typing import Optional, Dict

import requests

# ------------------------------------------------------------------
# 增加本地持久化缓存支持
# ------------------------------------------------------------------
from src.utils.llm_cache import get_cache  # noqa: E402

__all__ = ["ask", "LLMClientError"]

# ------------------------------------------------------------------
# Dedicated logger for LLM traffic
# 1) 使用单独的 logger，关闭 propagate 确保不会把大量 prompt
#    打到 console，保持终端输出简洁。
# 2) 仅添加 RotatingFileHandler 指向 logs/qa_pipeline.log。
# 3) 若外部已配置文件 handler，会自动复用，避免重复。
# ------------------------------------------------------------------
_LLM_LOGGER_NAME = "llm_traffic"
_DEFAULT_LLM_LOG_FILE = "logs/llm_traffic.log"  # Fallback log file


def _flush_all_loggers() -> None:
    """强制刷新所有日志处理器的缓冲区"""
    # 先刷新专用LLM日志
    llm_logger = logging.getLogger(_LLM_LOGGER_NAME)
    for handler in llm_logger.handlers:
        handler.flush()
    
    # 再刷新根日志器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        handler.flush()


def _get_llm_logger() -> logging.Logger:  # noqa: D401
    """Initializes and returns a dedicated logger for LLM traffic.

    This logger automatically writes to the same file as the root logger's
    file handler, if one is configured. This allows LLM traffic from different
    processes (e.g., ingestion, QA) to be consolidated into the appropriate log file.

    If no file handler is found on the root logger, it falls back to a default.
    """
    logger = logging.getLogger(_LLM_LOGGER_NAME)
    if not logger.handlers:  # Initialize only once
        logger.setLevel(logging.INFO)
        logger.propagate = False  # Avoid duplicating logs to console

        # --- Determine log file path dynamically ---
        root_logger = logging.getLogger()
        file_handler = next((h for h in root_logger.handlers if isinstance(h, logging.FileHandler)), None)
        log_file = file_handler.baseFilename if file_handler else _DEFAULT_LLM_LOG_FILE

        from pathlib import Path
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)

        # Add a handler to write to the determined log file
        handler = RotatingFileHandler(
            log_file, maxBytes=5 * 1024 * 1024, backupCount=3, encoding="utf-8"
        )
        fmt = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        handler.setFormatter(logging.Formatter(fmt))
        logger.addHandler(handler)
    return logger


_llm_logger = _get_llm_logger()

# ------------------------------------------------------------------
# Helper functions for readable logging
# ------------------------------------------------------------------


def _pretty_payload(payload: Dict, *, width: int = 120) -> str:  # noqa: D401
    """Return a pretty-printed JSON string with wrapped message content."""
    p = deepcopy(payload)
    for msg in p.get("messages", []):
        content = msg.get("content")
        if not isinstance(content, str):
            continue
        # 保留原有换行，对每一段做折行
        wrapped_segments = [
            textwrap.fill(seg, width=width) if len(seg) > width else seg
            for seg in content.splitlines()
        ]
        msg["content"] = "\n".join(wrapped_segments)
    s = json.dumps(p, ensure_ascii=False, indent=2)
    # json.dumps 会把换行转义为 \n，这里替换回来便于阅读
    return s.replace("\\n", "\n")


def _wrap_text(text: str, *, width: int = 120) -> str:  # noqa: D401
    """Wrap long text to specified width for log readability."""
    lines = text.splitlines() or [text]
    wrapped = [
        textwrap.fill(line, width=width) if len(line) > width else line
        for line in lines
    ]
    return "\n".join(wrapped)


def _extract_json_block(text: str) -> str:  # noqa: D401
    """Extract the *first* JSON object/array from arbitrary text.

    1. 优先匹配 ```json code block```
    2. 否则采用 *非贪婪* 正则提取第一个 `{...}` 或 `[...]`，
       以避免包含后续解释文字导致 `Extra data` 解析错误。
    """

    # --- 1) ```json ... ``` 代码块 ---
    match = re.search(r"```json\s*(\{[\s\S]*?\}|\[[\s\S]*?\])\s*```", text)
    if match:
        return match.group(1)

    # --- 2) 非贪婪匹配第一个 JSON object ---
    obj_match = re.search(r"\{[\s\S]*?\}", text)
    if obj_match:
        return obj_match.group(0)

    # --- 3) 非贪婪匹配第一个 JSON array ---
    arr_match = re.search(r"\[[\s\S]*?\]", text)
    if arr_match:
        return arr_match.group(0)

    # Fallback: return original text (likely to raise JSON error upstream)
    return text


# PROVIDER = os.getenv("LLM_PROVIDER", "siliconflow").lower()
PROVIDER = os.getenv("LLM_PROVIDER", "ollama").lower()
# _DEFAULT_MODEL = os.getenv("LLM_MODEL", "Qwen/Qwen3-8B")
_DEFAULT_MODEL = os.getenv("LLM_MODEL", "qwen3:latest")

# SiliconFlow settings
_SF_URL = "https://api.siliconflow.cn/v1/chat/completions"
_SF_KEY_ENV = "SILICONFLOW_API_KEY"

# Ollama settings (local)
_OL_URL = os.getenv("OLLAMA_URL", "http://**************:11434/api/chat")

# Gientech settings
_GT_URL = os.getenv("GIENTECH_URL", "http://gtai-api.*************.nip.io/v1/chat/completions")
_GT_KEY_ENV = "GIENTECH_API_KEY"

_TIMEOUT = float(os.getenv("LLM_TIMEOUT", "60"))


class LLMClientError(RuntimeError):
    """Raised when SiliconFlow API call fails."""

    # NOTE: We override __init__ so that **whenever** this exception is raised, a
    # conspicuous warning is emitted to the console.  This helps users notice
    # that the system has fallen back to heuristic logic because the LLM
    # service is currently unavailable or returned an invalid response.
    def __init__(self, message: str) -> None:  # noqa: D401
        # Compose a highlighted warning message (red & bold if the terminal
        # supports ANSI colours).
        warn_msg = (
            "\033[91m\033[1m[警告] 大模型调用失败，已回退到简化模式，"
            "结果可能不准确！\033[0m\n详情: " + str(message)
        )

        # 1) 直接写到 stderr —— 确保即使日志系统尚未初始化，也能看到提示
        import sys

        try:
            sys.stderr.write(warn_msg + "\n")
        except Exception:  # noqa: BLE001
            # Fallback to print if sys.stderr is unavailable (unit tests etc.)
            print(warn_msg)

        # 2) 同时使用 logging 记录，便于文件追踪与后续分析
        import logging as _logging

        _logging.getLogger(__name__).error(warn_msg)
        _flush_all_loggers()  # 确保错误日志立即写入文件

        super().__init__(message)


def _sf_headers() -> Dict[str, str]:
    api_key = os.getenv(_SF_KEY_ENV)
    if not api_key:
        raise LLMClientError("SILICONFLOW_API_KEY not set for SiliconFlow provider")
    return {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }


# ------------------------------------------------------------------
# Public API
# ------------------------------------------------------------------

def ask(
    prompt: str,
    *,
    system: Optional[str] = None,
    model: Optional[str] = None,
    provider: Optional[str] = None,
    temperature: float = 0.2,
    max_tokens: int = 4096,
    response_format: Optional[Dict] = None,
    think: Optional[bool] = None,
) -> str:
    """Query SiliconFlow chat completion endpoint and return assistant content."""

    from src.utils.config import load_config  # local import to avoid cycles

    # --------------------------------------------------------------
    # 缓存开关：通过环境变量控制
    # --------------------------------------------------------------
    cache_disabled = os.getenv("LLM_CACHE_DISABLE", "0") in {"1", "true", "True"}
    cache = None if cache_disabled else get_cache()

    cfg = load_config()
    services_cfg = cfg.get("llm_services", {})  # type: ignore[arg-type]

    # Determine provider (call-level override > argument > env variable)
    chosen_provider = (provider or os.getenv("LLM_PROVIDER") or PROVIDER).lower()

    # Provider-specific config shortcut
    prov_cfg_global = services_cfg.get(chosen_provider, {})

    # Timeout：优先读取 provider 配置，其次读取环境变量，最后用默认 30s
    timeout_sec = float(prov_cfg_global.get("timeout", os.getenv("LLM_TIMEOUT", _TIMEOUT)))

    # Determine model (call-level override > argument > default per provider)
    if model is None:
        if chosen_provider in services_cfg:
            model = services_cfg[chosen_provider].get("default_model", _DEFAULT_MODEL)
        else:
            model = _DEFAULT_MODEL

    if think is None:
        # 缺省视为开启思考模式
        think = True

    # --------------------------------------------------------------
    # 1) 先尝试读取缓存
    # --------------------------------------------------------------
    cache_key_params = {
        "provider": chosen_provider,
        "model": model,
        "system": system or "",
        "prompt": prompt,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "response_format": response_format,
        "think": think,
    }

    if cache:
        cached_resp = cache.get(cache_key_params)
        if cached_resp is not None:
            _llm_logger.info("LLM 缓存命中，直接返回结果")
            return cached_resp

    payload: Dict
    headers: Dict[str, str]
    url: str

    if chosen_provider == "siliconflow":
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": system or "You are a helpful assistant."},
                {"role": "user", "content": prompt},
            ],
            "temperature": temperature,
            "max_tokens": max_tokens,
        }
        # SiliconFlow 当前不支持 response_format 参数
        # 同时必须提供 temperature 和 max_tokens 参数

        # Endpoint & API key env may be overridden in config
        prov_cfg = services_cfg.get("siliconflow", {})
        url = prov_cfg.get("endpoint", _SF_URL)

        # Allow custom API key env var name
        key_env = prov_cfg.get("api_key_env", _SF_KEY_ENV)

        def _sf_headers_dyn() -> Dict[str, str]:  # noqa: D401
            api_key = os.getenv(key_env)
            if not api_key:
                raise LLMClientError(f"{key_env} not set for SiliconFlow provider")
            return {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}",
            }

        headers = _sf_headers_dyn()

    elif chosen_provider == "gientech":
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": system or "You are a helpful assistant."},
                {"role": "user", "content": prompt},
            ],
            "temperature": temperature,
            "top_p": 0.7,  # Default top_p; can be customized later via payload
            "max_tokens": max_tokens,
            "think": think,  # Explicitly pass think flag
        }
        # Gientech 当前接口不支持 OpenAI-style response_format，因此忽略该参数以避免 400 错误。

        prov_cfg = services_cfg.get("gientech", {})
        url = prov_cfg.get("endpoint", _GT_URL)

        key_env = prov_cfg.get("api_key_env", _GT_KEY_ENV)

        def _gt_headers_dyn() -> Dict[str, str]:  # noqa: D401
            api_key = os.getenv(key_env)
            if not api_key:
                raise LLMClientError(f"{key_env} not set for Gientech provider")
            return {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}",
            }

        headers = _gt_headers_dyn()

    elif chosen_provider == "ollama":
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": system or "You are a helpful assistant."},
                {"role": "user", "content": prompt},
            ],
            "stream": False,
            "think": think,  # 始终包含 think 字段
        }
        if response_format is not None:
            payload["format"] = "json"

        # Host override from config (may be base URL without path)
        prov_cfg = services_cfg.get("ollama", {})
        raw_host = prov_cfg.get("host")
        if raw_host:
            url = raw_host.rstrip("/")
            if "/api/" not in url:
                url += "/api/chat"
        else:
            url = _OL_URL
        headers = {"Content-Type": "application/json"}

    else:
        raise LLMClientError(f"Unsupported LLM provider: {chosen_provider}")

    # --------------------------------------------------------------
    # 记录完整的请求 payload，方便排查 prompt / 参数等问题。
    # 使用 INFO 级别，确保在默认日志级别下即可写入文件。
    # --------------------------------------------------------------
    try:
        _llm_logger.info("LLM request payload (provider=%s):\n%s", chosen_provider, _pretty_payload(payload))
        _flush_all_loggers()  # 立即刷新日志确保请求被记录
    except Exception as _log_err:  # noqa: BLE001
        _llm_logger.warning("Failed to log LLM request payload: %s", _log_err)
        _flush_all_loggers()  # 确保错误日志也能立即写入文件

    # ------------------------------------------------------------------
    # 执行 HTTP 请求，允许一次重试：
    # 1) 首次调用失败 → 暂停 1s → 再调用一次
    # 2) 第二次仍失败 → 抛出 LLMClientError 供上层处理
    # ------------------------------------------------------------------

    resp = None
    for attempt in range(2):  # 最多尝试 2 次
        try:
            # Skip system proxies when calling local Ollama service to prevent SOCKS errors
            if chosen_provider == "ollama":
                resp = requests.post(
                    url,
                    headers=headers,
                    data=json.dumps(payload),
                    timeout=timeout_sec,
                    proxies={"http": None, "https": None},  # type: ignore[arg-type]
                )
            else:
                resp = requests.post(url, headers=headers, data=json.dumps(payload), timeout=timeout_sec)
            resp.raise_for_status()
            break  # 成功后跳出循环
        except requests.RequestException as e:  # noqa: BLE001
            if attempt == 0:
                logging.warning("LLM 调用失败，1 秒后重试一次：%s", e)
                _flush_all_loggers()  # 确保警告信息立即写入日志文件
                import time

                time.sleep(1)
                continue
            raise LLMClientError(f"HTTP error: {e}") from e

    if resp is None:  # 理论上不应发生
        raise LLMClientError("LLM request failed without response")

    content: str
    try:
        data = resp.json()
        if chosen_provider in ("siliconflow", "gientech"):
            content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
        else:
            # Ollama response is always nested in message.content, even with format="json"
            content = data.get("message", {}).get("content", "")
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        raise LLMClientError(f"Malformed response: {resp.text[:200]}") from e

    # If JSON output was expected, try to clean up the response by extracting the JSON block
    if response_format:
        content = _extract_json_block(content)

    # 记录完整响应，同样用 INFO 级别方便追踪。
    _llm_logger.info("LLM response:\n%s", _wrap_text(content))
    _flush_all_loggers()  # 确保响应内容立即写入日志文件

    # --------------------------------------------------------------
    # 2) 缓存结果
    # --------------------------------------------------------------
    if cache:
        cache.set(cache_key_params, content)

    return content 