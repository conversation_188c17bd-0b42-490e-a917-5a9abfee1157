from __future__ import annotations
from typing import List, Dict, Any
import sqlite3

import pandas as pd  # type: ignore
from rich.console import Console
from rich.markdown import Markdown
import logging

from rich.table import Table
# Live 表格动态刷新
from rich.live import Live
# CLI Prompt
from rich.prompt import Prompt

# CLI 选项解析
import argparse
import os

from ..utils.logger import setup_logging
from ..orchestration.pipeline import MixedPipeline
import json


"""QA Pipeline：在终端交互式展示问答全过程（向量召回 + MCP 查询）。"""


class QAPipeline:  # noqa: D101
    def __init__(self, db_path: str = "output/data.db", *, top_k_sheets: int = 3, top_n_rows: int = 5) -> None:  # noqa: D401
        self.console = Console()
        self.db_path = db_path
        self.top_k_sheets = top_k_sheets
        self.top_n_rows = top_n_rows
        # QA环境日志只写文件，不输出到console
        # 调高日志级别到 DEBUG 以捕获更丰富信息
        setup_logging(level=logging.DEBUG, console=False)

        # QA环境不添加console输出，所有日志信息只写入文件
        self.logger = logging.getLogger("qa.pipeline")

        # 直接使用 MixedPipeline 作为执行引擎
        self.pipeline = MixedPipeline("config/qa_pipeline.yaml")

        # 启动时打印 MCP 能力看板
        self._display_capabilities()

    # ------------------------------------------------------------------
    def _display_capabilities(self) -> None:  # noqa: D401
        """已废弃占位方法：原用于展示 MCP 能力，目前留空以保持兼容。"""
        return

    def run_question(self, question: str) -> None:  # noqa: D401
        self.console.rule(f"🧠 [bold cyan]问题[/]: {question}")

        # ------------------------------
        # 1) 构建并执行 MixedPipeline
        # ------------------------------
        self.pipeline.build(question)
        components = self.pipeline.components
        comp_names = [getattr(c, "name", c.__class__.__name__) for c in components]

        import sys
        use_live = sys.stdout.isatty() and self.console.is_terminal

        if use_live:
            statuses = ["[dim]□[/]"] * len(comp_names)
            timings = [""] * len(comp_names)

            def _render() -> Table:  # noqa: D401
                tbl = Table(show_header=False, box=None)
                tbl.add_column("", width=3)
                tbl.add_column("Task")
                tbl.add_column("Time", justify="right", style="dim")
                SUBTASKS = {"sheet_meta", "mcp_query", "row_lookup", "column_lookup"}
                for st, nm, timing in zip(statuses, comp_names, timings):
                    indent = "  " if nm in SUBTASKS else ""
                    tbl.add_row(st, f"{indent}{nm}", timing)
                return tbl

            def _progress(name: str, dur: float | None, _state: Dict[str, Any]):  # noqa: D401
                idx = comp_names.index(name)
                if dur is None:
                    # 开始执行
                    statuses[idx] = "[cyan]▶[/]"
                else:
                    statuses[idx] = "[green]✓[/]"
                    timings[idx] = f"{dur:.2f}s" if dur else ""
                live.update(_render())

            with Live(_render(), console=self.console, refresh_per_second=4, transient=True) as live:
                result: Dict[str, Any] = self.pipeline.execute({"query": question}, progress_cb=_progress)

        else:
            # 非 TTY：一次性执行
            result: Dict[str, Any] = self.pipeline.execute({"query": question})
        timings_info = result.get("_timings", [])

        # ------------------------------
        # 2) 组装表格并打印（TTY 亦或非 TTY 都一次性输出）
        # ------------------------------
        statuses: list[str] = []
        timings: list[str] = []
        for comp in comp_names:
            t = next((d["duration"] for d in timings_info if d["name"] == comp), None)
            if t is not None:
                statuses.append("[green]✓[/]")
                timings.append(f"{t:.2f}s")
            else:
                statuses.append("[dim]—[/]")
                timings.append("")

        table = Table(show_header=False, box=None)
        table.add_column("", width=3)
        table.add_column("Task")
        table.add_column("Time", justify="right", style="dim")
        SUBTASKS = {"sheet_meta", "mcp_query", "row_lookup", "column_lookup"}
        for st, nm, timing in zip(statuses, comp_names, timings):
            indent = "  " if nm in SUBTASKS else ""
            table.add_row(st, f"{indent}{nm}", timing)

        self.console.print(table)

        # 若未生成 answer 字段，填充占位字符串，保持兼容上游逻辑
        answer = result
        if "answer" not in answer:
            answer["answer"] = "(无答案)"
        # ---------------- Debug / Trace ----------------
        # 打印执行的组件顺序，便于定位是否调用了向量召回、MCP、LLM 等
        component_names = [getattr(c, "name", c.__class__.__name__) for c in self.pipeline.components]
        self.console.print("[bold blue]执行组件顺序:[/] " + " → ".join(component_names))

        # 打印意图识别结果
        if intent := answer.get("intent"):
            self.console.print(f"[bold magenta]识别意图:[/] {intent}")
        if strategy := answer.get("strategy"):
            self.console.print(f"[bold magenta]策略:[/] {strategy} {answer.get('strategy_params', {})}")

        # 新增：打印 file/sheet/row 层级概率分布，便于调试
        level_probs_raw = answer.get("level_probs")
        if isinstance(level_probs_raw, dict) and level_probs_raw:
            # 格式化输出，例如 {file:0.2, sheet:0.5, row:0.3}
            formatted = ", ".join(f"{k}: {v:.2f}" for k, v in level_probs_raw.items())
            self.console.print(f"[bold magenta]层级概率分布:[/] {{{formatted}}}")

        # 打印向量召回结果（若有）
        if sheet_ids := answer.get("sheet_ids"):
            self.console.print(f"[bold magenta]向量召回 SheetIDs:[/] {sheet_ids}")

        # 新增：行级向量召回 & Rerank 结果 (Top5)
        def _print_rows(tag: str, rows: list[dict]):  # noqa: D401
            tbl = Table(title=tag, show_lines=True)
            tbl.add_column("sheet_id", style="cyan", justify="right")
            tbl.add_column("rowid", style="green", justify="right")
            tbl.add_column("text", overflow="fold")
            for r in rows[:5]:
                tbl.add_row(str(r.get("sheet_id")), str(r.get("rowid")), str(r.get("text", ""))[:120])
            self.console.print(tbl)

        if isinstance(answer.get("row_texts_raw"), list) and answer["row_texts_raw"]:
            _print_rows("行级召回 (Vector) Top5", answer["row_texts_raw"])

        if isinstance(answer.get("row_texts"), list) and answer["row_texts"]:
            _print_rows("行级召回 (Rerank) Top5", answer["row_texts"])

        # ---------------- MCP 调用记录 ----------------
        mcp_calls = answer.get("mcp_calls")
        if isinstance(mcp_calls, list) and mcp_calls:
            tbl = Table(title="MCP 调用记录", show_lines=True)
            tbl.add_column("序号", style="cyan", justify="right")
            tbl.add_column("工具名称", style="magenta")
            tbl.add_column("参数", overflow="fold")
            tbl.add_column("结果", overflow="fold")
            for idx, rec in enumerate(mcp_calls, 1):
                args_str = json.dumps(rec.get("args", {}), ensure_ascii=False)
                result_str = str(rec.get("result", ""))[:120]
                tbl.add_row(str(idx), str(rec.get("name")), args_str, result_str)
            self.console.print(tbl)

        # 如果调用了 LLM 生成答案，可通过 answer 字段判断
        self.console.print("[bold magenta]LLM 是否参与:[/] " + ("是" if answer.get("answer") else "否"))
        # ------------------------------------------------

        self.console.rule("💡 [bold green]AI 回答")
        ans_text = answer.get("answer", "（无回答）")
        try:
            self.console.print(Markdown(ans_text))
        except Exception:  # noqa: BLE001
            # 若 Markdown 渲染失败，回退为普通文本
            self.console.print(ans_text)
        # ---------------- 表格预览展示策略 ----------------
        preview = answer.get("tables_preview")

        show_preview_flag: bool | None = answer.get("show_preview")  # LLM 可显式指示是否展示

        preview_tables_filter = answer.get("preview_tables")  # LLM 可指定要展示的表名列表

        def _should_show() -> bool:  # noqa: D401
            if show_preview_flag is not None:
                return bool(show_preview_flag)
            # 回退：若用户在问题里包含预览关键词
            preview_keywords = ["预览", "示例行", "preview"]
            return any(kw in question for kw in preview_keywords)

        if isinstance(preview, dict) and preview and _should_show():
            items = preview.items()
            if isinstance(preview_tables_filter, list) and preview_tables_filter:
                items = [(nm, rows) for nm, rows in preview.items() if nm in preview_tables_filter]

            for name, rows in items:
                self._display_table_preview(name, pd.DataFrame(rows))

    # ------------------------------------------------------------------
    # helpers
    # ------------------------------------------------------------------
    def _fetch_sheet_metadata(self, sheet_ids: List[str]):  # noqa: D401
        if not sheet_ids:
            return []
        ids_num = [int(s.split(":", 1)[1]) for s in sheet_ids]
        placeholders = ",".join("?" for _ in ids_num)
        conn = sqlite3.connect(self.db_path)
        cur = conn.execute(
            f"SELECT sheet_id, sheet_name, description FROM sheet_metadata WHERE sheet_id IN ({placeholders})",
            ids_num,
        )
        rows = cur.fetchall()
        conn.close()
        return rows

    def _display_sheets(self, sheets_info):  # noqa: D401
        table = Table(title="召回 Sheet", show_lines=True)
        table.add_column("sheet_id", style="cyan", justify="right")
        table.add_column("sheet_name", style="magenta")
        table.add_column("description", overflow="fold")
        for sid, name, desc in sheets_info:
            table.add_row(str(sid), str(name), str(desc))
        self.console.print(table)

    def _display_rows(self, sheet_id: int, df_rows: pd.DataFrame):  # noqa: D401
        table = Table(title=f"Sheet {sheet_id} 行级召回", show_lines=True)
        table.add_column("rowid", style="cyan", justify="right")
        table.add_column("text", overflow="fold")
        for _, row in df_rows.iterrows():
            table.add_row(str(row["rowid"]), str(row["text"]))
        self.console.print(table)

    def _display_table_preview(self, name: str, df: pd.DataFrame):  # noqa: D401
        # 1) 删除全空列，避免空白表
        df_clean = df.dropna(axis=1, how="all")

        # 若仍然全部为空，则不展示
        if df_clean.empty or df_clean.columns.empty:
            self.logger.debug("表 %s 预览为空或仅含空列，已跳过展示", name)
            return

        # 限制最大展示列数，防止终端宽度爆炸
        max_cols = 10
        if len(df_clean.columns) > max_cols:
            df_clean = df_clean.iloc[:, :max_cols]

        table = Table(title=f"表 {name} 示例行", show_lines=True)
        for col in df_clean.columns:
            table.add_column(str(col))
        for _, row in df_clean.iterrows():
            table.add_row(*[str(v) for v in row.values])
        self.console.print(table)

    # ------------------------------------------------------------------
    # Fallback helpers
    # ------------------------------------------------------------------
    def _extract_keywords(self, question: str) -> List[str]:  # noqa: D401
        import re

        tokens = re.findall(r"[\u4e00-\u9fa5A-Za-z0-9]+", question)
        # 过滤停用词可扩展，这里简单返回长度>1 的 token
        return [t for t in tokens if len(t) > 1]

    def _keyword_search_sheets(self, keywords: List[str]) -> List[str]:  # noqa: D401
        if not keywords:
            return []
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()
        like_clauses = ["description LIKE ? OR sheet_name LIKE ?" for _ in keywords]
        sql = f"SELECT sheet_id FROM sheet_metadata WHERE {' OR '.join(like_clauses)} LIMIT ?"
        params: List[Any] = []
        for kw in keywords:
            like = f"%{kw}%"
            params.extend([like, like])
        params.append(self.top_k_sheets)
        cur.execute(sql, params)
        rows = cur.fetchall()
        conn.close()
        return [f"sheet:{r[0]}" for r in rows]


# ----------------------------------------------------------------------
# ASCII 彩色徽标生成
# ----------------------------------------------------------------------

def _gradient_hex(start: str, end: str, steps: int) -> list[str]:  # noqa: D401
    """生成从 ``start`` 到 ``end`` 的渐变色列表，长度为 ``steps``。"""
    def _hex_to_rgb(h: str) -> tuple[int, int, int]:  # noqa: D401
        h = h.lstrip("#")
        return tuple(int(h[i : i + 2], 16) for i in (0, 2, 4))  # type: ignore[misc]

    def _rgb_to_hex(rgb: tuple[int, int, int]) -> str:  # noqa: D401
        return "{:02X}{:02X}{:02X}".format(*rgb)

    rgb_start = _hex_to_rgb(start)
    rgb_end = _hex_to_rgb(end)

    colors: list[str] = []
    for i in range(steps):
        ratio = i / max(steps - 1, 1)
        rgb = tuple(
            int(rgb_start[j] + (rgb_end[j] - rgb_start[j]) * ratio) for j in range(3)
        )
        colors.append(_rgb_to_hex(rgb))
    return colors


def _build_logo() -> str:  # noqa: D401
    """构建 >HIERQA 风格的 LOGO，无边框，带渐变色和精确阴影。"""

    # ------------------------------------------------------------------
    # 1. 重新定义 7 行高的 ASCII 图案：右向箭头 + H I E R Q A
    # 每个字符宽度统一为 8；字符之间空 1 格，箭头与首字符之间空 2 格
    # ------------------------------------------------------------------

    arrow = [
        "██      ",
        " ██     ",
        "  ███   ",
        "   ████ ",
        "  ███   ",
        " ██     ",
        "██      ",
    ]

    H = [
        "██    ██",
        "██    ██",
        "██    ██",
        "████████",
        "██    ██",
        "██    ██",
        "██    ██",
    ]

    I = [
        "████████",
        "   ██   ",
        "   ██   ",
        "   ██   ",
        "   ██   ",
        "   ██   ",
        "████████",
    ]

    E = [
        "████████",
        "██      ",
        "██      ",
        "██████  ",
        "██      ",
        "██      ",
        "████████",
    ]

    R = [
        "██████  ",
        "██   ██ ",
        "██   ██ ",
        "██████  ",
        "██ ██   ",
        "██  ██  ",
        "██   ██ ",
    ]

    Q = [
        " ██████ ",
        "██    ██",
        "██    ██",
        "██    ██",
        "██ █ ██ ",
        "██   ██ ",
        " ███  ██",
    ]

    A = [
        "  ████  ",
        " ██  ██ ",
        "██    ██",
        "████████",
        "██    ██",
        "██    ██",
        "██    ██",
    ]

    letters = [H, I, E, R, Q, A]

    rows: list[str] = []
    for i in range(7):  # 7 行
        line_parts = [arrow[i], "  "]  # 箭头 + 两空格
        for idx, ch in enumerate(letters):
            line_parts.append(ch[i])
            if idx < len(letters) - 1:
                line_parts.append("  ")  # 字符之间空 2 格
        rows.append("".join(line_parts))

    art_lines = rows

    # 2. 定义辅助函数和参数
    def _darken_hex(h: str, factor: float = 0.5) -> str:
        """按给定因子调暗十六进制颜色。"""
        h = h.lstrip("#")
        rgb = [int(h[i : i + 2], 16) for i in (0, 2, 4)]
        dark_rgb = tuple(int(c * factor) for c in rgb)
        return "{:02X}{:02X}{:02X}".format(*dark_rgb)

    art_height = len(art_lines)
    art_width = len(art_lines[0])
    output_height = art_height + 1
    output_width = art_width + 2 # 阴影需要额外空间

    # 3. 生成颜色
    # 从左到右的水平渐变
    colors = _gradient_hex("#4DA1FF", "#D65BFF", art_width)
    shadow_colors = [_darken_hex(c) for c in colors]

    # 4. 构建输出缓冲区（画布）
    buffer = [[" "] * output_width for _ in range(output_height)]

    # 5. 绘制内容和阴影到缓冲区
    for y, line in enumerate(art_lines):
        for x, char in enumerate(line):
            if char == "█":
                color = colors[x]
                shadow_color = shadow_colors[x]

                # 绘制阴影（右下方偏移）
                if y + 1 < output_height and x + 1 < output_width:
                    buffer[y + 1][x + 1] = f"[#{shadow_color}]█[/]"
                
                # 绘制主块
                buffer[y][x] = f"[#{color}]█[/]"

    # 6. 从缓冲区生成最终的输出字符串
    output_lines = ["".join(row) for row in buffer]

    # 移除末尾可能存在的空行
    while output_lines and not output_lines[-1].strip():
        output_lines.pop()

    logo_body = "\n".join(output_lines)
    return "\n\n" + logo_body + "\n\n"


# 公开常量，供 CLI 启动时打印
QA_ASCII_LOGO = _build_logo()

# ----------------------------------------------------------------------
# CLI 入口
# ----------------------------------------------------------------------

def main() -> None:  # noqa: D401
    parser = argparse.ArgumentParser(description="Hierarchical Data QA Console")
    parser.add_argument("--no-cache", action="store_true", help="禁用 LLM 缓存")
    args = parser.parse_args()

    if args.no_cache:
        os.environ["LLM_CACHE_DISABLE"] = "1"

    console = Console()
    # 在启动时先打印彩色 ASCII 图标
    console.print(QA_ASCII_LOGO)
    pipeline = QAPipeline()
    console.print("[bold green]Hierarchical Data QA Console[/]")
    while True:
        try:
            q = Prompt.ask("[bold yellow]请输入问题 (exit 退出) >[/]")
            # 在非交互管道输入的场景下，Prompt.ask 不会自动输出换行符，导致后续分隔符与提示粘连
            # 因此这里手动打印一个空行，确保格式正常。
            console.print()
        except (EOFError, KeyboardInterrupt):
            console.print("\n再见！")
            break
        if q.lower() in {"exit", "quit", "q"}:
            break
        if not q.strip():
            continue
        pipeline.run_question(q)


if __name__ == "__main__":
    main() 