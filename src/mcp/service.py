import json
import logging
import os
import asyncio
import requests
from typing import Dict, Any, List, Union, Tuple

# fastmcp 可能在运行环境中未安装，这里做兼容处理 ------------------------------
try:
    from fastmcp import FastMCP, Client  # type: ignore
except ModuleNotFoundError:  # pragma: no cover
    FastMCP = None  # type: ignore
    Client = None  # type: ignore

"""
虚拟 MCP（Metadata Catalog Platform）服务模块。

提供 `query_lineage(table, field)` 用于根据表名和字段名返回血缘信息。
该模块同时包含 FastMCP 服务器实现和客户端调用函数。
"""

logger = logging.getLogger(__name__)

# ===========================================================================
# 1. 服务器端实现
# ===========================================================================

# ---------------------------------------------------------------------------
# 初始化 FastMCP 服务器实例
# ---------------------------------------------------------------------------
if FastMCP:
    mcp = FastMCP("LineageMCP")
else:
    logger.warning("FastMCP 未安装，使用占位 MCP 对象。")

    class _DummyMCP:  # noqa: D401
        """占位 MCP，用于在缺少 FastMCP 时避免 ImportError。"""

        def tool(self, *args, **kwargs):  # noqa: D401
            # 装饰器，直接返回原函数
            def _decorator(func):  # noqa: D401
                return func

            return _decorator

    mcp = _DummyMCP()

# ---------------------------------------------------------------------------
# 内置的模拟血缘数据
# ---------------------------------------------------------------------------
_MOCK_LINEAGE_DATA: Dict[Tuple[str, str], Dict[str, str]] = {
    ("sales", "amount"): {
        "field": "sales.amount",
        "upstream": "orders.amount",
        "downstream": "revenue.total_amount",
        "description": "字段 sales.amount 的上游来源为 orders.amount，下游去向为 revenue.total_amount。",
    },
    ("orders", "order_id"): {
        "field": "orders.order_id",
        "upstream": "staging_orders.oid",
        "downstream": "sales.order_id, finance.order_ref",
        "description": "字段 orders.order_id 的上游来源为 staging_orders.oid，下游去向为 sales.order_id、finance.order_ref。",
    },
    ("CBRC_EAST5_0_业务Mapping_V2_2_4_员工表", "证件号码"): {
        "field": "员工表.证件号码",
        "upstream": "HRMS.employee.id_number",
        "downstream": "report.employee_id_masked",
        "description": "员工表中的证件号码来源于 HRMS.employee.id_number，脱敏后写入报表。",
    },
    ("CBRC_EAST5_0_业务Mapping_V2_2_4_员工表", "证件类型"): {
        "field": "员工表.证件类型",
        "upstream": "HRMS.employee.id_type",
        "downstream": "report.employee_id_type",
        "description": "员工表中的证件类型来源于 HRMS.employee.id_type，脱敏后写入报表。",
    },
}

# ---------------------------------------------------------------------------
# 服务器端：内部实现函数
# ---------------------------------------------------------------------------
def _query_lineage_impl(table: str, field: Union[str, List[str]]) -> str:
    """【服务器端】查询血缘信息的具体逻辑。"""
    logger.info("[Server] 调用虚拟 MCP：table=%s field=%s", table, field)
    if isinstance(field, list):
        results: List[Dict[str, str]] = []
        for f in field:
            item = _MOCK_LINEAGE_DATA.get((table, f)) or {"message": f"未找到 {table}.{f} 的数据血缘信息。"}
            results.append(item)
        result_json = json.dumps(results, ensure_ascii=False)
    else:
        data = _MOCK_LINEAGE_DATA.get((table, field)) or {"message": f"未找到 {table}.{field} 的数据血缘信息。"}
        result_json = json.dumps(data, ensure_ascii=False)
    logger.info("[Server] MCP 返回: %s", result_json)
    return result_json

# ---------------------------------------------------------------------------
# 服务器端：将内部函数注册为 MCP 工具
# ---------------------------------------------------------------------------
if FastMCP:
    @mcp.tool(name="query_lineage", description="查询指定表字段的数据血缘信息")
    def query_lineage_tool(table: str, field: Union[str, List[str]]) -> str:
        return _query_lineage_impl(table, field)


# ===========================================================================
# 2. 客户端实现 (使用 fastmcp.Client)
# ===========================================================================

# 服务器地址
MCP_SERVER_URL = "http://127.0.0.1:8484/mcp/"

def _run_async_in_sync(coro):
    """在同步函数中运行异步代码的辅助函数。"""
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    if loop.is_running():
        task = loop.create_task(coro)
        return loop.run_until_complete(asyncio.gather(task))[0]
    else:
        return loop.run_until_complete(coro)


async def _list_mcp_services_async() -> str:
    """【异步客户端】列出所有可用的 MCP 工具。"""
    try:
        if FastMCP and Client:
            async with Client(MCP_SERVER_URL) as client:
                logger.debug("[Client] Listing tools...")
                tools = await client.list_tools()
                tools_schema = [t.model_dump() for t in tools]
                return json.dumps(tools_schema, ensure_ascii=False)
        else:
            logger.warning("[Client] FastMCP 或 Client 未安装，无法列出 MCP 工具。")
            return json.dumps({"error": "MCP 客户端未安装"})
    except Exception as e:
        logger.error("[Client] Failed to list tools from MCP server: %s", e, exc_info=True)
        return json.dumps({"error": f"无法从 MCP 服务器列出工具: {e}"})

async def _query_lineage_async(table: str, field: Union[str, List[str]]) -> str:
    """【异步客户端】查询数据血缘信息。"""
    try:
        if FastMCP and Client:
            async with Client(MCP_SERVER_URL) as client:
                logger.debug("[Client] Calling tool 'query_lineage' with params: table=%s, field=%s", table, field)
                result = await client.call_tool("query_lineage", {"table": table, "field": field})
                return result.content
        else:
            logger.warning("[Client] FastMCP 或 Client 未安装，无法调用 MCP 工具。")
            return json.dumps({"error": "MCP 客户端未安装"})
    except Exception as e:
        logger.error("[Client] Failed to call 'query_lineage': %s", e, exc_info=True)
        return json.dumps({"error": f"调用 'query_lineage' 工具失败: {e}"})

# --- 同步封装，供项目其他部分调用 ---

def list_mcp_services() -> str:
    """返回可用 MCP 工具列表（JSON 字符串）。

    优先尝试通过 FastMCP 客户端获取；若不可用，则回退到本地静态定义。
    """

    logger.debug("[Client] Listing tools...")

    # --- 尝试通过 FastMCP 服务获取 -------------------------------------------------
    if FastMCP and Client:
        try:
            tools_json = _run_async_in_sync(_list_mcp_services_async())
            if tools_json and tools_json != "[]":
                return tools_json
        except Exception as e:  # noqa: BLE001
            logger.warning("通过 FastMCP 获取工具列表失败，回退本地静态定义: %s", e)

    # --- 回退：返回本地静态定义 ---------------------------------------------------
    static_tools = [
        {
            "name": "query_lineage",
            "description": "查询指定表字段的数据血缘信息",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "table": {"type": "string", "title": "Table"},
                    "field": {
                        "anyOf": [
                            {"type": "string"},
                            {"type": "array", "items": {"type": "string"}},
                        ],
                        "title": "Field",
                    },
                },
                "required": ["table", "field"],
            },
        }
    ]
    return json.dumps(static_tools, ensure_ascii=False)

def clean_tools_for_api(tools: List[Dict[str, Any]], provider: str = "siliconflow") -> List[Dict[str, Any]]:
    """清理工具定义，使其与不同API提供商兼容。
    
    Args:
        tools: 原始工具定义列表
        provider: API提供商名称
        
    Returns:
        List[Dict[str, Any]]: 清理后的工具定义列表
    """
    if not tools:
        return []
    
    cleaned_tools = []
    
    for tool in tools:
        # OpenAI / SiliconFlow 采用 {type:function,function:{...}} 结构
        if provider.lower() in ("siliconflow", "openai", "gientech"):
            cleaned_tool = {
                "type": "function",
                "function": {
                    "name": tool.get("name", ""),
                    "description": tool.get("description", ""),
                },
            }
            fn_obj = cleaned_tool["function"]
        else:
            cleaned_tool = {
                "name": tool.get("name", ""),
                "description": tool.get("description", ""),
            }
            fn_obj = cleaned_tool  # directly add params later
 
        # 处理输入schema
        if "inputSchema" in tool:
            # 对于SiliconFlow，使用更简洁的工具格式
            if provider.lower() in ("siliconflow", "openai", "gientech"):
                # 转换为parameters格式
                input_schema = tool["inputSchema"]
                parameters = {
                    "type": "object",
                    "properties": input_schema.get("properties", {}),
                    "required": input_schema.get("required", [])
                }
                fn_obj["parameters"] = parameters
            else:
                # 其他提供商保持原格式
                cleaned_tool["inputSchema"] = tool["inputSchema"]
        
        # 移除不必要的字段
        if provider.lower() in ("siliconflow", "openai", "gientech"):
            # 这几个提供商不需要 outputSchema
            pass
        elif "outputSchema" in tool:
            cleaned_tool["outputSchema"] = tool["outputSchema"]
            
        cleaned_tools.append(cleaned_tool)
    
    return cleaned_tools

def query_lineage(table: str, field: Union[str, List[str]]) -> str:
    """查询指定表字段的数据血缘信息。

    若 FastMCP 可用，则通过其客户端调用；否则直接调用本地实现。"""

    # --- 尝试远程调用 -----------------------------------------------------------
    if FastMCP and Client:
        try:
            result = _run_async_in_sync(_query_lineage_async(table=table, field=field))
            return result
        except Exception as e:  # noqa: BLE001
            logger.warning("通过 FastMCP 查询血缘失败，回退本地实现: %s", e)

    # --- 本地实现 ---------------------------------------------------------------
    return _query_lineage_impl(table, field)


# ===========================================================================
# 3. 启动服务器与自我测试
# ===========================================================================

async def run_self_test():
    """异步自测函数。"""
    print("\n" + "="*20 + " MCP Client Self-Test " + "="*20)

    # 测试 1: 列出服务
    print("\n[Test 1] Calling list_mcp_services()...")
    services_json = await _list_mcp_services_async()
    print("Response:")
    try:
        print(json.dumps(json.loads(services_json), indent=2, ensure_ascii=False))
    except (json.JSONDecodeError, TypeError):
        print(services_json)

    # 测试 2: 查询一个存在的血缘
    print("\n[Test 2] Calling query_lineage for ('sales', 'amount')...")
    lineage_json = await _query_lineage_async(table="sales", field="amount")
    print("Response:")
    try:
        print(json.dumps(json.loads(lineage_json), indent=2, ensure_ascii=False))
    except (json.JSONDecodeError, TypeError):
        print(lineage_json)

    # 测试 3: 查询一个不存在的血缘
    print("\n[Test 3] Calling query_lineage for a non-existent item...")
    non_existent_json = await _query_lineage_async(table="inventory", field="quantity")
    print("Response:")
    try:
        print(json.dumps(json.loads(non_existent_json), indent=2, ensure_ascii=False))
    except (json.JSONDecodeError, TypeError):
        print(non_existent_json)

    print("\n" + "="*20 + " Self-Test Complete " + "="*24)


if __name__ == "__main__":
    import time
    import threading

    def run_server():
        print("Starting MCP server at", MCP_SERVER_URL)
        if FastMCP:
            mcp.run(transport="http", host="0.0.0.0", port=8484)
        else:
            print("FastMCP 未安装，无法启动 MCP 服务器。")

    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    time.sleep(1)

    try:
        asyncio.run(run_self_test())
    except Exception as e:
        print(f"\nAn error occurred during self-test: {e}")

    print("\nMCP server is running. Press Ctrl+C to stop.")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping MCP server.")