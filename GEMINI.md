# Gemini代码助手项目概览

本文档由Gemini代码助手生成，旨在提供 `hierarchical-data-server` 项目的高级概览。

## 项目概述

`hierarchical-data-server` 是一个基于Python的应用，旨在提供对层级结构数据（主要来源于Excel文件）的查询服务。它通过一个FastAPI服务器暴露一个问答（QA）接口，允许用户使用自然语言对底层数据进行提问。

该系统使用一个数据提取管道来处理和向量化源数据，并将生成的嵌入（embeddings）存储在Chroma向量库中，以实现高效的语义搜索和检索。其核心逻辑涉及意图分类、一个多步骤的问答管道，以及为提供精确答案而设计的多个组件的编排。

## 关键组件

- **`src/server.py`**: FastAPI Web服务器的主入口文件。
- **`src/ingestion/`**: 包含数据提取逻辑，负责解析Excel文件 (`excel_parser.py`) 和执行提取管道 (`pipeline.py`)。
- **`src/qa/`**: 包含问答管道及相关组件。
- **`src/orchestration/`**: 管理业务逻辑流程，并协调问答系统中的不同组件。它包含一个 `components` 子目录，其中有许多用于意图分类、数据检索和SQL生成等任务的专用组件。
- **`src/vectorization/`**: 负责使用模型创建和管理数据的向量嵌入。
- **`src/storage/`**: 包含数据库相关逻辑，可能用于存储元数据或关系型数据。
- **`scripts/`**: 包含用于特定任务的辅助脚本，例如分析数据模式。
- **`data_files/`**: 用于存放输入数据文件（如源Excel电子表格）的指定目录。
- **`config/`**: 存储YAML格式的应用配置文件。
- **`vector_store/`**: Chroma向量数据库存储其数据的默认目录。
- **`tests/`**: 包含项目的测试套件。
- **`logs/`**: 包含应用的日志文件，如 `llm_traffic.log` 和 `qa_pipeline.log`。
- **`output/`**: 用于存放生成文件的目录，如 `data.db` 和 `sample_data.xlsx`。

## 常用命令

以下是运行、测试和管理该应用的一些常用命令。

- **安装依赖**:
  ```sh
  uv pip install -e .
  ```

- **运行FastAPI服务器**:
  ```sh
  uvicorn src.server:app --reload
  ```

- **运行测试**:
  ```sh
  pytest
  ```

- **运行数据提取**:
  项目为此定义了一个自定义脚本。
  ```sh
  ingest-excel
  ```

- **运行控制台问答管道**:
  项目为此定义了一个自定义脚本，用于通过控制台与QA系统交互。
  ```sh
  qa-console
  ```

- **分析数据模式**:
  一个用于分析数据文件模式的辅助脚本。
  ```sh
  analyze-schema
  ```
