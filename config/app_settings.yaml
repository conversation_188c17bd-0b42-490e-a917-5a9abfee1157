llm_services:
  # === 可用的大模型服务 ===
  # 若添加新的服务，请在此处补充。
  ollama:
    host: "http://**************:11434"
    # 默认模型可按需覆盖
    default_model: qwen3:latest

  siliconflow:
    # 硅基流动示例配置（如需使用请取消注释并完善）
    endpoint: "https://api.siliconflow.cn/v1/chat/completions"
    rerank_endpoint: "https://api.siliconflow.cn/v1/rerank"
    api_key_env: SILICONFLOW_API_KEY
    default_model: Qwen/Qwen3-30B-A3B 
    timeout: 120  # 请求超时时间（秒）

  gientech:
    # 中电金信大模型服务配置
    endpoint: "http://gtai-api.*************.nip.io/v1/chat/completions"
    embeddings_endpoint: "http://gtai-api.*************.nip.io/v1/embeddings"
    api_key_env: GIENTECH_API_KEY
    default_model: Qwen3-30B-A3B

# =====================================================================
# 全局日志配置
# =====================================================================

logging:
  # 日志级别，可选 DEBUG / INFO / WARNING / ERROR / CRITICAL
  level: DEBUG


# =====================================================================
# 任务级别配置：为每个任务单独指定使用的服务 / 模型 / prompt 等信息
# =====================================================================

tasks:

  # ------------------- 表头识别 -------------------
  header_detection:
    provider: ollama
    model: qwen3:latest
    # provider: gientech
    # model: Qwen3-30B-A3B
    # provider: siliconflow 
    # model: Qwen/Qwen3-30B-A3B
    think: false
    system_prompt: |
      你是一名专业的数据表解析专家。请严格遵循用户指令，仅以 JSON 对象形式回答。

  # ------------------- 表格 Sheet 摘要 -------------------
  sheet_summary:
    provider: ollama
    model: qwen3:latest
    # provider: gientech
    # model: Qwen3-30B-A3B
    think: true            # 是否启用思考模式（默认开启）
    prompt: |
      你是一位中文数据分析助手，善于用一句话概括表格用途。可以包含一些表格中的关键词，字段等。
      请直接给出结果，不要包含任何思考过程,不要超过 200 个字。

  # ------------------- 文件 File 摘要 -------------------
  file_summary:
    provider: ollama
    model: qwen3:latest
    # provider: gientech
    # model: Qwen3-30B-A3B
    think: true
    prompt: |
      你是一位中文数据分析助手，善于总结 Excel 文件主要内容。可以包含一些文件中的关键词，字段等。
      请只输出最终一句中文，不要包含思考过程, 不要超过 200 个字。

  # ------------------- 文本向量化 Embedding -------------------
  embedding:
    provider: ollama
    model: dengcao/Qwen3-Embedding-4B:Q5_K_M
    # model: bge-m3:latest  # 默认中文多语语义向量模型
    # provider: gientech
    # model: bge-m3
    think: true
