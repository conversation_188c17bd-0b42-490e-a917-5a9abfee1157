# 意图识别规则 - 仅保留 file / sheet / row 三个层级，用作大模型候选及关键词回退

- intent: row
  keywords: ["行", "记录", "数据行", "明细", "具体值"]
  strategy: vector_row
  params: {level: row, top_k: 20}
  examples:
    - "明细科目编号是什么格式？"
    - "证件类别填写有什么要求？"
    - "交易渠道的代码是什么？"
    - "用户 ID 为 123 的交易记录有哪些？"
    - "编号 456 的行中金额是多少？" 
    - "自营资金业务余额表中哪些字段的默认值是 99991231？"
    - "哪些列的缺省值为 0？"
    - "字段 '结束日期' 的默认值是什么？"

- intent: sheet
  keywords: ["表", "sheet", "工作表", "汇总", "整表", "指标数", "频度"]
  strategy: vector_sheet
  params: {level: sheet, top_k: 5}
  examples:
    - "资产负债表包含哪些列？"
    - "明细表的更新频度是多少？"
    - "一表通业务有哪些表？"
    - "个人贷款明细表的统计口径是什么？"
    - "表G01资产负债项目统计表的用途是什么？"

- intent: file
  keywords: ["文件", "workbook", "总体", "全局", "报告级"]
  strategy: vector_sheet
  params: {level: file, top_k: 3}
  examples:
    - "监管报送有哪些业务？"

# 新增：结构化 SQL 查询意图 -------------------------------
- intent: sql_query
  keywords: ["统计", "总数", "平均", "筛选", "SQL", "select ", "where ", "count(", "sum("]
  strategy: sql_pipeline
  params: {}
  examples:
    - "统计 2023 年各地区的放款总额"
    - "查询状态为已结清的合同数量"
    - "平均还款金额是多少？" 
      