fixed_steps:
  - name: intent_classifier
    enabled: true
    inputs: [query]
    outputs: [intent, strategy, strategy_params, intent_result, level_probs]
    config:
      # provider: siliconflow
      # model: Qwen/Qwen3-30B-A3B 
      # provider: ollama
      # model: qwen3:latest
      provider: siliconflow
      # model: moonshotai/Kimi-K2-Instruct 
      model: Qwen/Qwen3-235B-A22B-Instruct-2507
      think: true
      min_prob: 0.3
      prompt: |
        你是一个查询意图分类器, 识别和理解用户输入的问题，进行分类。
        我们的数据是监管领域的知识数据，存储在多个Excel文件中，每个文件分为多个
        sheet，每个sheet中存储表格分为多个row。
        文件通常是不同业务领域的数据上报映射关系，比如：一表通、EAST等。
        每个sheet存储该上报业务对应的不同表，比如：G01资产负债项目统计表、个人信贷分账户明细等。
        每个表中的row，通常对应具体的字段信息, 比如：明细科目编号、科目名称等。

        要求返回一个 JSON 对象， 包含 file、sheet、row 三个键，各自对应概率 (0-1 之间，且和为 1)。
        仅回复 JSON，无需解释。 比如：{"file":0.1,"sheet":0.2,"row":0.7}

        若用户问题与上述监管数据完全无关，请直接回答 **不相关**（无需 JSON）!
      mock: false
      mock_output: {"file":0.9,"sheet":0.1,"row":0.1}
  - name: sheet_name_classifier
    enabled: true
    inputs: [query]
    outputs: [selected_table, sheet_ids]
    # 跳过条件：当意图识别为“文件级(file)”时，跳过本组件
    skip_if: "state.get('intent') == 'file'"
    config:
      provider: ollama
      model: qwen3:latest
      min_conf: 0.5
      prompt: |
        你是一个表名匹配助手，任务是从给定表名列表中找出最可能被用户问题提及的那张表。
        如果无法确定，请回复 JSON {{ "sheet_name": "", "confidence": 0 }}。
        如果匹配到了，请回复 JSON {{ "sheet_name": <表名>, "confidence": <置信度> }}。
        严格仅返回 JSON 对象，无需其它解释文字。
        用户问题：{question}
        表名列表：
        {sheet_list}
  - name: data_item_identifier
    enabled: true
    inputs: [query, sheet_ids, selected_table]
    outputs: [identified_data_item, confidence]
    # 仅当意图识别为“行级(row)”时才执行本组件
    when: "state.get('intent') == 'row'"  
    config:
      provider: ollama
      model: qwen3:latest
      prompt: |
        你是一个精准的实体识别专家。
        根据用户的问题和给定的“数据项”列表，你的任务是判断出问题具体指向哪个(或哪几个)数据项。
        “数据项”列表如下: [{item_list}]
        用户的问题是: "{question}"

        请返回一个JSON对象，包含两个键:
        1. "data_item": 你认为问题所指的、且在列表中的数据项名称。如果问题看起来与列表中的任何数据项都无关，则返回 null。 多个数据项的时候，key 为 "data_items"，value 为列表。
        2. "confidence": 一个0.0到1.0之间的浮点数，表示你本次判断的置信度。

        返回示例:
        {
          "data_item": "贷款合同编号",
          "confidence": 0.95
        }
        另一个示例 (多个数据项):
        {
          "data_items": ["贷款合同编号", "贷款合同名称"],
          "confidence": [0.95, 0.90]
        }

        另一个示例 (无法明确匹配):
        {
          "data_item": null,
          "confidence": 0.2
        }

        现在，请分析问题和数据项列表，并给出你的JSON格式的回复。
  - name: query_expander
    enabled: true
    inputs: [query]
    outputs: [keywords]
    skip_if: "bool(state.get('row_texts'))"
    config:
      provider: ollama
      model: qwen3:latest
      think: false
  - name: row_vector_recall
    enabled: true
    inputs: [query, intent]
    outputs: [row_texts, sheet_ids, level_probs]
    skip_if: "bool(state.get('row_texts'))"
    config:
      top_k_rows: 100  # 行级向量召回数量
      top_n_rows: 5   # 最终返回的行数
      kw_weight: 0.3  # 关键词权重
  - name: sheet_file_vector_recall
    enabled: true
    inputs: [query]
    outputs: [query_vector, sheet_ids, retrieved_ids, file_texts, sheet_meta, row_texts]
    skip_if: "bool(state.get('row_texts'))"
    config:
      topk: 100   # 每层级最多召回 100 条向量
      embedding_provider: ollama
      embedding_model: dengcao/Qwen3-Embedding-4B:Q5_K_M
  - name: row_bm25_recall
    enabled: true
    inputs: [query, intent, row_texts]
    outputs: [row_texts]
    skip_if: "bool(state.get('row_texts'))"
    config:
      enabled: true 
      top_k_rows: 500   # 扩大初始召回
      top_n_rows: 40    # 传给 rerank 的候选行数
  - name: rerank
    enabled: true
    inputs: [query, row_texts]
    outputs: [row_texts]
    skip_if: "bool(state.get('row_texts'))"
    config:
      top_k: 10          # 输出给 LLM 的行数
      provider: siliconflow
      model: Qwen/Qwen3-Reranker-4B
      bm25_weight: 2.0   # 增强 BM25 权重
  - name: llm_answer
    enabled: true
    inputs: [query, row_texts, sheet_meta, column_texts, file_texts, sql_result]
    outputs: [answer]
    config:
      provider: siliconflow
      # model: moonshotai/Kimi-K2-Instruct 
      # model: Qwen/Qwen3-30B-A3B 
      model: Qwen/Qwen3-235B-A22B-Instruct-2507
      # provider: gientech 
      # model: Qwen3-30B-A3B 
      think: true
      enable_tool_calling: true  # 允许大模型自主调用 MCP 工具
      # 使用组件默认的 system_prompt（已内置工具调用说明），如需自定义请确保包含工具描述
